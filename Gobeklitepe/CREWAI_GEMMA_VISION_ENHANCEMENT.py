#!/usr/bin/env python3
"""
🚀 CrewAI + Gemma Vision Enhancement System
==========================================

Advanced multi-agent system leveraging full Gemma potential with image processing.
Images normalized to 896x896 resolution and encoded to 256 tokens each.
Integrated with existing database configurations.

Features:
- CrewAI specialized teams for HVAC operations
- Gemma Vision for equipment image analysis
- Advanced customer profiling with visual data
- Database integration with existing configs
- Real-time image processing pipeline
"""

import asyncio
import base64
import io
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from PIL import Image, ImageOps
import numpy as np

# CrewAI imports
from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool
from langchain.tools import Tool
from langchain.llms.base import LLM

# Database imports
import psycopg2
from pymongo import MongoClient
import redis

# Configuration from existing files
MONGODB_URL = "******************************************************"
POSTGRES_CONFIG = {
    'host': '**************',
    'port': '5432', 
    'database': 'hvacdb',
    'user': 'hvacdb',
    'password': 'blaeritipol'
}
REDIS_CONFIG = {
    'host': 'localhost',
    'port': 6379,
    'db': 0
}
LM_STUDIO_URL = "http://*************:1234"

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ImageAnalysisResult:
    """Result of image analysis with Gemma Vision"""
    image_path: str
    equipment_type: str
    brand: str
    model: str
    condition_assessment: str
    issues_detected: List[str]
    maintenance_recommendations: List[str]
    confidence_score: float
    processed_tokens: int
    analysis_timestamp: datetime


@dataclass
class CustomerVisualProfile:
    """Enhanced customer profile with visual data"""
    customer_id: str
    equipment_images: List[ImageAnalysisResult]
    installation_photos: List[str]
    service_photos: List[str]
    visual_timeline: List[Dict[str, Any]]
    equipment_condition_trend: Dict[str, float]


class GemmaVisionProcessor:
    """
    Advanced image processor using Gemma Vision capabilities
    Normalizes images to 896x896 and encodes to 256 tokens
    """
    
    def __init__(self):
        self.target_size = (896, 896)
        self.token_count = 256
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        
    def normalize_image(self, image_path: str) -> Tuple[str, Dict[str, Any]]:
        """
        Normalize image to 896x896 resolution for optimal Gemma processing
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Calculate aspect ratio preserving resize
                img_ratio = img.width / img.height
                target_ratio = self.target_size[0] / self.target_size[1]
                
                if img_ratio > target_ratio:
                    # Image is wider, fit to width
                    new_width = self.target_size[0]
                    new_height = int(new_width / img_ratio)
                else:
                    # Image is taller, fit to height
                    new_height = self.target_size[1]
                    new_width = int(new_height * img_ratio)
                
                # Resize image
                img_resized = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # Create canvas and center image
                canvas = Image.new('RGB', self.target_size, (255, 255, 255))
                paste_x = (self.target_size[0] - new_width) // 2
                paste_y = (self.target_size[1] - new_height) // 2
                canvas.paste(img_resized, (paste_x, paste_y))
                
                # Convert to base64 for API
                buffer = io.BytesIO()
                canvas.save(buffer, format='JPEG', quality=95)
                img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                
                metadata = {
                    'original_size': (img.width, img.height),
                    'normalized_size': self.target_size,
                    'aspect_ratio_preserved': True,
                    'file_size_bytes': len(buffer.getvalue()),
                    'estimated_tokens': self.token_count
                }
                
                return img_base64, metadata
                
        except Exception as e:
            logger.error(f"Error normalizing image {image_path}: {e}")
            return None, {}
    
    async def analyze_hvac_equipment_image(self, image_path: str, context: str = "") -> ImageAnalysisResult:
        """
        Analyze HVAC equipment image using Gemma Vision
        """
        try:
            # Normalize image
            img_base64, metadata = self.normalize_image(image_path)
            if not img_base64:
                raise ValueError("Failed to normalize image")
            
            # Create specialized HVAC analysis prompt
            prompt = f"""
            Analyze this HVAC equipment image and provide detailed technical assessment.
            
            Context: {context}
            
            Please provide a JSON response with the following structure:
            {{
                "equipment_type": "klimatyzacja|pompa_ciepla|rekuperator|wentylator|kocioł|other",
                "brand": "detected brand name",
                "model": "detected model if visible",
                "condition_assessment": "excellent|good|fair|poor|critical",
                "issues_detected": ["list of visible issues or problems"],
                "maintenance_recommendations": ["specific maintenance actions needed"],
                "technical_observations": ["detailed technical notes"],
                "installation_quality": "professional|adequate|poor|unknown",
                "safety_concerns": ["any safety issues observed"],
                "estimated_age": "age estimate in years or unknown",
                "replacement_priority": "low|medium|high|urgent",
                "confidence_score": 0.0-1.0
            }}
            
            Focus on:
            - Equipment identification and condition
            - Visible wear, damage, or maintenance needs
            - Installation quality and safety
            - Professional HVAC technician perspective
            """
            
            # Call Gemma Vision via LM Studio
            response = await self._call_gemma_vision(img_base64, prompt)
            
            if response:
                return ImageAnalysisResult(
                    image_path=image_path,
                    equipment_type=response.get('equipment_type', 'unknown'),
                    brand=response.get('brand', 'unknown'),
                    model=response.get('model', 'unknown'),
                    condition_assessment=response.get('condition_assessment', 'unknown'),
                    issues_detected=response.get('issues_detected', []),
                    maintenance_recommendations=response.get('maintenance_recommendations', []),
                    confidence_score=response.get('confidence_score', 0.0),
                    processed_tokens=metadata.get('estimated_tokens', 256),
                    analysis_timestamp=datetime.now()
                )
            else:
                raise ValueError("No response from Gemma Vision")
                
        except Exception as e:
            logger.error(f"Error analyzing image {image_path}: {e}")
            return ImageAnalysisResult(
                image_path=image_path,
                equipment_type='unknown',
                brand='unknown', 
                model='unknown',
                condition_assessment='unknown',
                issues_detected=[],
                maintenance_recommendations=[],
                confidence_score=0.0,
                processed_tokens=0,
                analysis_timestamp=datetime.now()
            )
    
    async def _call_gemma_vision(self, image_base64: str, prompt: str) -> Optional[Dict[str, Any]]:
        """Call Gemma Vision API via LM Studio"""
        try:
            import aiohttp
            
            payload = {
                "model": "gemma-2-2b-it",  # Adjust model name for vision capabilities
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an expert HVAC technician with 20+ years experience. Analyze equipment images with professional precision."
                    },
                    {
                        "role": "user", 
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                "temperature": 0.2,
                "max_tokens": 1000
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{LM_STUDIO_URL}/v1/chat/completions",
                    json=payload,
                    timeout=60
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result['choices'][0]['message']['content']
                        
                        # Extract JSON from response
                        try:
                            json_start = content.find('{')
                            json_end = content.rfind('}') + 1
                            if json_start != -1 and json_end != -1:
                                json_str = content[json_start:json_end]
                                return json.loads(json_str)
                        except json.JSONDecodeError:
                            logger.warning("Failed to parse JSON from Gemma Vision response")
                            
                    return None
                    
        except Exception as e:
            logger.error(f"Error calling Gemma Vision: {e}")
            return None


class HVACImageAnalysisTool(BaseTool):
    """CrewAI tool for HVAC image analysis"""
    
    name: str = "hvac_image_analyzer"
    description: str = "Analyze HVAC equipment images to identify type, condition, and maintenance needs"
    
    def __init__(self):
        super().__init__()
        self.vision_processor = GemmaVisionProcessor()
    
    def _run(self, image_path: str, context: str = "") -> str:
        """Run image analysis synchronously"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                self.vision_processor.analyze_hvac_equipment_image(image_path, context)
            )
            return json.dumps(asdict(result), default=str, indent=2)
        finally:
            loop.close()


class EnhancedHVACCrewSystem:
    """
    Enhanced CrewAI system with Gemma Vision integration
    Specialized teams for comprehensive HVAC operations
    """
    
    def __init__(self):
        self.vision_processor = GemmaVisionProcessor()
        self.image_tool = HVACImageAnalysisTool()
        
        # Database connections using existing configs
        self.mongo_client = MongoClient(MONGODB_URL)
        self.mongo_db = self.mongo_client['hvac_enhanced_crm']
        self.postgres_conn = psycopg2.connect(**POSTGRES_CONFIG)
        self.redis_client = redis.Redis(**REDIS_CONFIG)
        
        # Initialize specialized agents
        self._create_specialized_agents()
        
    def _create_specialized_agents(self):
        """Create specialized HVAC agents with enhanced capabilities"""
        
        # Equipment Visual Inspector Agent
        self.visual_inspector = Agent(
            role='HVAC Equipment Visual Inspector',
            goal='Analyze equipment images to assess condition and identify maintenance needs',
            backstory="""You are a master HVAC technician with exceptional visual diagnostic skills.
            You can identify equipment brands, models, and conditions from images with 95%+ accuracy.
            Your expertise covers all HVAC systems: klimatyzacja, pompy ciepła, rekuperatory, kotły.""",
            verbose=True,
            allow_delegation=False,
            tools=[self.image_tool]
        )
        
        # Customer Communication Specialist
        self.communication_specialist = Agent(
            role='Customer Communication Intelligence Specialist', 
            goal='Analyze customer communications to extract insights and predict needs',
            backstory="""You are an expert in customer relationship management with deep understanding
            of HVAC customer behavior patterns. You excel at sentiment analysis, need prediction,
            and creating personalized service recommendations.""",
            verbose=True,
            allow_delegation=True
        )
        
        # Technical Documentation Agent
        self.documentation_agent = Agent(
            role='Technical Documentation and Knowledge Manager',
            goal='Create comprehensive technical documentation and maintain equipment knowledge base',
            backstory="""You are a technical writer specializing in HVAC systems documentation.
            You create detailed service reports, maintenance guides, and equipment specifications
            that help technicians and customers understand complex HVAC systems.""",
            verbose=True,
            allow_delegation=False
        )
        
        # Predictive Maintenance Analyst
        self.maintenance_analyst = Agent(
            role='Predictive Maintenance and Analytics Specialist',
            goal='Analyze equipment data to predict failures and optimize maintenance schedules',
            backstory="""You are a data scientist specializing in HVAC predictive maintenance.
            You use historical data, visual inspections, and customer feedback to predict
            equipment failures and optimize maintenance schedules for maximum efficiency.""",
            verbose=True,
            allow_delegation=True
        )
    
    def create_visual_equipment_assessment_crew(self, customer_id: str, image_paths: List[str]) -> Crew:
        """Create crew for comprehensive visual equipment assessment"""
        
        tasks = []
        
        # Visual inspection task
        visual_task = Task(
            description=f"""
            Perform comprehensive visual assessment of HVAC equipment for customer {customer_id}.
            
            Image paths to analyze: {image_paths}
            
            For each image:
            1. Identify equipment type, brand, and model
            2. Assess current condition and any visible issues
            3. Recommend immediate maintenance actions
            4. Estimate remaining equipment lifespan
            5. Identify safety concerns
            
            Provide detailed technical report with prioritized recommendations.
            """,
            agent=self.visual_inspector,
            expected_output="Comprehensive visual assessment report with equipment analysis and recommendations"
        )
        tasks.append(visual_task)
        
        # Documentation task
        doc_task = Task(
            description=f"""
            Create comprehensive technical documentation based on visual assessment results.
            
            Include:
            1. Equipment inventory with specifications
            2. Condition assessment summary
            3. Maintenance schedule recommendations
            4. Safety compliance checklist
            5. Customer communication summary
            
            Format as professional service report.
            """,
            agent=self.documentation_agent,
            expected_output="Professional technical documentation and service report"
        )
        tasks.append(doc_task)
        
        # Predictive analysis task
        prediction_task = Task(
            description=f"""
            Analyze visual assessment data to create predictive maintenance plan.
            
            Consider:
            1. Equipment age and condition trends
            2. Historical maintenance patterns
            3. Seasonal usage factors
            4. Cost optimization opportunities
            5. Risk assessment for equipment failures
            
            Provide 12-month maintenance forecast with budget estimates.
            """,
            agent=self.maintenance_analyst,
            expected_output="Predictive maintenance plan with 12-month forecast and budget"
        )
        tasks.append(prediction_task)
        
        return Crew(
            agents=[self.visual_inspector, self.documentation_agent, self.maintenance_analyst],
            tasks=tasks,
            process=Process.sequential,
            verbose=True
        )
    
    def create_customer_intelligence_crew(self, customer_id: str, email_data: List[Dict], transcription_data: List[Dict]) -> Crew:
        """Create crew for advanced customer intelligence analysis"""
        
        tasks = []
        
        # Communication analysis task
        comm_task = Task(
            description=f"""
            Analyze all customer communications for customer {customer_id}.
            
            Email data: {len(email_data)} emails
            Transcription data: {len(transcription_data)} transcriptions
            
            Extract:
            1. Communication patterns and preferences
            2. Sentiment trends over time
            3. Service satisfaction indicators
            4. Equipment concerns and issues
            5. Upsell and cross-sell opportunities
            
            Create comprehensive customer intelligence profile.
            """,
            agent=self.communication_specialist,
            expected_output="Comprehensive customer intelligence profile with insights and recommendations"
        )
        tasks.append(comm_task)
        
        # Predictive customer analysis
        customer_prediction_task = Task(
            description=f"""
            Create predictive customer analysis based on communication intelligence.
            
            Predict:
            1. Churn probability and risk factors
            2. Next service needs and timing
            3. Equipment upgrade opportunities
            4. Customer lifetime value trends
            5. Optimal communication strategies
            
            Provide actionable recommendations for customer retention and growth.
            """,
            agent=self.maintenance_analyst,
            expected_output="Predictive customer analysis with retention and growth strategies"
        )
        tasks.append(customer_prediction_task)
        
        return Crew(
            agents=[self.communication_specialist, self.maintenance_analyst],
            tasks=tasks,
            process=Process.sequential,
            verbose=True
        )
    
    async def process_customer_with_visual_data(self, customer_id: str, image_paths: List[str], 
                                              email_data: List[Dict] = None, 
                                              transcription_data: List[Dict] = None) -> Dict[str, Any]:
        """
        Complete customer processing with visual data integration
        """
        logger.info(f"🚀 Processing customer {customer_id} with visual data")
        
        results = {
            'customer_id': customer_id,
            'processing_timestamp': datetime.now(),
            'visual_analysis': {},
            'customer_intelligence': {},
            'recommendations': [],
            'next_actions': []
        }
        
        try:
            # Process images with Gemma Vision
            if image_paths:
                logger.info(f"📸 Analyzing {len(image_paths)} images")
                visual_crew = self.create_visual_equipment_assessment_crew(customer_id, image_paths)
                visual_results = visual_crew.kickoff()
                results['visual_analysis'] = visual_results
                
                # Store visual analysis in databases
                await self._store_visual_analysis(customer_id, visual_results)
            
            # Process communications if provided
            if email_data or transcription_data:
                logger.info(f"💬 Analyzing communications: {len(email_data or [])} emails, {len(transcription_data or [])} transcriptions")
                comm_crew = self.create_customer_intelligence_crew(
                    customer_id, email_data or [], transcription_data or []
                )
                comm_results = comm_crew.kickoff()
                results['customer_intelligence'] = comm_results
                
                # Store communication analysis
                await self._store_communication_analysis(customer_id, comm_results)
            
            # Generate unified recommendations
            results['recommendations'] = await self._generate_unified_recommendations(results)
            
            # Cache results in Redis
            await self._cache_customer_results(customer_id, results)
            
            logger.info(f"✅ Completed processing for customer {customer_id}")
            return results
            
        except Exception as e:
            logger.error(f"❌ Error processing customer {customer_id}: {e}")
            results['error'] = str(e)
            return results
    
    async def _store_visual_analysis(self, customer_id: str, analysis_results: Any):
        """Store visual analysis results in databases"""
        try:
            # MongoDB - raw analysis data
            doc = {
                'customer_id': customer_id,
                'analysis_type': 'visual_equipment_assessment',
                'results': str(analysis_results),
                'timestamp': datetime.now(),
                'processed_by': 'crewai_gemma_vision'
            }
            self.mongo_db.visual_analyses.insert_one(doc)
            
            # PostgreSQL - structured data
            with self.postgres_conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO customer_visual_analyses 
                    (customer_id, analysis_type, results, timestamp)
                    VALUES (%s, %s, %s, %s)
                """, (customer_id, 'visual_equipment', str(analysis_results), datetime.now()))
            self.postgres_conn.commit()
            
            logger.info(f"✅ Stored visual analysis for customer {customer_id}")
            
        except Exception as e:
            logger.error(f"❌ Error storing visual analysis: {e}")
    
    async def _store_communication_analysis(self, customer_id: str, analysis_results: Any):
        """Store communication analysis results"""
        try:
            # MongoDB
            doc = {
                'customer_id': customer_id,
                'analysis_type': 'communication_intelligence',
                'results': str(analysis_results),
                'timestamp': datetime.now(),
                'processed_by': 'crewai_communication_specialist'
            }
            self.mongo_db.communication_analyses.insert_one(doc)
            
            # PostgreSQL
            with self.postgres_conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO customer_communication_analyses 
                    (customer_id, analysis_type, results, timestamp)
                    VALUES (%s, %s, %s, %s)
                """, (customer_id, 'communication_intelligence', str(analysis_results), datetime.now()))
            self.postgres_conn.commit()
            
            logger.info(f"✅ Stored communication analysis for customer {customer_id}")
            
        except Exception as e:
            logger.error(f"❌ Error storing communication analysis: {e}")
    
    async def _generate_unified_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate unified recommendations from all analysis results"""
        recommendations = []
        
        # Extract recommendations from visual analysis
        if 'visual_analysis' in results:
            recommendations.append("Schedule equipment maintenance based on visual assessment")
            recommendations.append("Update equipment inventory with current condition status")
        
        # Extract recommendations from communication analysis
        if 'customer_intelligence' in results:
            recommendations.append("Implement personalized communication strategy")
            recommendations.append("Schedule proactive customer outreach")
        
        # Add general recommendations
        recommendations.extend([
            "Update customer profile with latest analysis results",
            "Schedule follow-up assessment in 6 months",
            "Consider equipment upgrade opportunities"
        ])
        
        return recommendations
    
    async def _cache_customer_results(self, customer_id: str, results: Dict[str, Any]):
        """Cache results in Redis for fast access"""
        try:
            cache_key = f"customer_analysis:{customer_id}"
            cache_data = json.dumps(results, default=str)
            self.redis_client.setex(cache_key, 3600, cache_data)  # 1 hour TTL
            logger.info(f"✅ Cached results for customer {customer_id}")
        except Exception as e:
            logger.error(f"❌ Error caching results: {e}")


async def demo_enhanced_crewai_system():
    """Demo the enhanced CrewAI system with Gemma Vision"""
    logger.info("🚀 Starting Enhanced CrewAI + Gemma Vision Demo")
    
    # Initialize system
    crew_system = EnhancedHVACCrewSystem()
    
    # Demo data
    customer_id = "demo_customer_001"
    demo_image_paths = [
        "/path/to/hvac_unit_1.jpg",
        "/path/to/hvac_unit_2.jpg"
    ]
    
    demo_email_data = [
        {
            "subject": "Klimatyzacja nie działa",
            "content": "Dzień dobry, moja klimatyzacja LG przestała chłodzić...",
            "sender": "<EMAIL>"
        }
    ]
    
    demo_transcription_data = [
        {
            "content": "Klient zgłasza problem z pompą ciepła...",
            "confidence": 0.95,
            "duration": 180
        }
    ]
    
    # Process customer with all data types
    results = await crew_system.process_customer_with_visual_data(
        customer_id=customer_id,
        image_paths=demo_image_paths,
        email_data=demo_email_data,
        transcription_data=demo_transcription_data
    )
    
    # Display results
    print("\n" + "="*60)
    print("🎯 ENHANCED CREWAI + GEMMA VISION RESULTS")
    print("="*60)
    print(f"Customer ID: {results['customer_id']}")
    print(f"Processing Time: {results['processing_timestamp']}")
    print(f"Visual Analysis: {'✅ Completed' if results.get('visual_analysis') else '❌ Skipped'}")
    print(f"Communication Analysis: {'✅ Completed' if results.get('customer_intelligence') else '❌ Skipped'}")
    print(f"Recommendations: {len(results.get('recommendations', []))}")
    
    if results.get('recommendations'):
        print("\n📋 Key Recommendations:")
        for i, rec in enumerate(results['recommendations'], 1):
            print(f"   {i}. {rec}")
    
    print("\n✅ Demo completed successfully!")
    return results


if __name__ == "__main__":
    # Run demo
    asyncio.run(demo_enhanced_crewai_system())