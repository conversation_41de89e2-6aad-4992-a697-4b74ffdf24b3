#!/usr/bin/env python3
"""
🚀 HVAC Semantic Framework Setup Script
Automated setup and initialization for the most powerful HVAC semantic framework
"""

import subprocess
import sys
import os
import asyncio
from pathlib import Path

def run_command(command, description):
    """Run shell command with error handling"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def main():
    """Main setup function"""
    print("🚀 HVAC Semantic Framework Setup")
    print("🔥 Building the most powerful semantic analysis framework for HVAC operations")
    print("=" * 80)
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("❌ Python 3.9+ required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Setup steps
    setup_steps = [
        {
            "command": "pip install --upgrade pip",
            "description": "Upgrading pip"
        },
        {
            "command": "pip install -r hvac_semantic_framework/requirements.txt",
            "description": "Installing framework dependencies"
        },
        {
            "command": "python -m spacy download en_core_web_sm",
            "description": "Downloading spaCy English model"
        }
    ]
    
    # Execute setup steps
    for step in setup_steps:
        if not run_command(step["command"], step["description"]):
            print(f"❌ Setup failed at: {step['description']}")
            sys.exit(1)
    
    print("\n🐳 Docker Setup Instructions:")
    print("To start Weaviate vector database:")
    print("docker run -d -p 8080:8080 \\")
    print("  -e QUERY_DEFAULTS_LIMIT=25 \\")
    print("  -e AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true \\")
    print("  -e PERSISTENCE_DATA_PATH='/var/lib/weaviate' \\")
    print("  -e DEFAULT_VECTORIZER_MODULE='none' \\")
    print("  -e ENABLE_MODULES='text2vec-transformers' \\")
    print("  --name weaviate \\")
    print("  semitechnologies/weaviate:latest")
    
    print("\n🎯 Next Steps:")
    print("1. Start Weaviate: docker run -d -p 8080:8080 semitechnologies/weaviate:latest")
    print("2. Run demo: python demo_hvac_semantic_framework.py")
    print("3. Check status: curl http://localhost:8080/v1/meta")
    
    print("\n🎉 HVAC Semantic Framework setup complete!")
    print("🚀 Ready to revolutionize HVAC operations!")

if __name__ == "__main__":
    main()