# 🚀 HVAC Semantic Framework Requirements
# Most powerful semantic analysis framework for HVAC operations

# Core Framework Dependencies
weaviate-client>=4.9.0
sentence-transformers>=3.0.0
transformers>=4.40.0
torch>=2.0.0
spacy>=3.7.0
numpy>=1.24.0
pandas>=2.0.0

# Advanced NLP and Semantic Analysis
gensim>=4.3.0
nltk>=3.8.0
textblob>=0.17.0
scikit-learn>=1.3.0

# Agent Frameworks (Optional - install as needed)
# langgraph>=0.2.0  # Uncomment when available
# pydantic-ai>=0.1.0  # Uncomment when available

# Data Processing and Analytics
polars>=0.20.0
vaex>=4.17.0
dask>=2024.1.0

# Database and Storage
redis>=5.0.0
psycopg2-binary>=2.9.0
motor>=3.3.0  # MongoDB async driver
minio>=7.2.0

# Web and API
fastapi>=0.110.0
uvicorn>=0.29.0
httpx>=0.27.0
aiohttp>=3.9.0

# Monitoring and Logging
prometheus-client>=0.20.0
structlog>=24.1.0

# Development and Testing
pytest>=8.0.0
pytest-asyncio>=0.23.0
black>=24.0.0
isort>=5.13.0
mypy>=1.9.0

# Visualization and UI
gradio>=4.20.0
plotly>=5.19.0
matplotlib>=3.8.0

# Utilities
python-dotenv>=1.0.0
pydantic>=2.6.0
typer>=0.12.0
rich>=13.7.0
tqdm>=4.66.0

# Optional: GPU acceleration
# torch-audio>=2.0.0  # For audio processing
# accelerate>=0.28.0  # For model acceleration