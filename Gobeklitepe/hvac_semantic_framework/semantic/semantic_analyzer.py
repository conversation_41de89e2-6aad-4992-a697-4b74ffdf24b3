"""
🧠 Advanced Semantic Analyzer for HVAC Operations
Most powerful semantic analysis engine with domain-specific intelligence
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import re
import json

# Advanced NLP libraries
from sentence_transformers import SentenceTransformer
import spacy
from transformers import pipeline, AutoTokenizer, AutoModel
import torch
import numpy as np

@dataclass
class SemanticAnalysisResult:
    """Result of semantic analysis"""
    text: str
    embeddings: List[float]
    entities: List[Dict[str, Any]]
    sentiment: Dict[str, Any]
    intent: Dict[str, Any]
    keywords: List[str]
    hvac_concepts: List[str]
    urgency_score: float
    confidence: float
    processing_time: float

class SemanticAnalyzer:
    """
    🔥 MOST ADVANCED Semantic Analyzer for HVAC Domain
    
    Features:
    - Multi-model ensemble for maximum accuracy
    - HVAC domain-specific processing
    - Real-time semantic analysis
    - Advanced entity extraction
    - Sentiment and intent analysis
    - Urgency detection
    - Equipment identification
    - Technical issue classification
    """
    
    def __init__(self, model_config: Optional[Dict[str, Any]] = None):
        self.logger = logging.getLogger(__name__)
        self.model_config = model_config or self._get_default_config()
        
        # Initialize models
        self.sentence_transformer = None
        self.spacy_model = None
        self.sentiment_pipeline = None
        self.intent_classifier = None
        
        # HVAC domain knowledge
        self.hvac_equipment_terms = self._load_hvac_equipment_terms()
        self.hvac_issue_patterns = self._load_hvac_issue_patterns()
        self.urgency_indicators = self._load_urgency_indicators()
        
        # Performance metrics
        self.analysis_count = 0
        self.total_processing_time = 0.0
        
    def _get_default_config(self) -> Dict[str, Any]:
        """Default model configuration"""
        return {
            "sentence_transformer_model": "all-MiniLM-L6-v2",
            "spacy_model": "en_core_web_sm",
            "sentiment_model": "cardiffnlp/twitter-roberta-base-sentiment-latest",
            "intent_model": "microsoft/DialoGPT-medium",
            "device": "cuda" if torch.cuda.is_available() else "cpu",
            "max_length": 512,
            "batch_size": 32
        }
    
    async def initialize(self) -> bool:
        """
        🚀 Initialize all semantic analysis models
        """
        try:
            self.logger.info("🚀 Initializing Semantic Analyzer...")
            
            # Initialize Sentence Transformer
            self.sentence_transformer = SentenceTransformer(
                self.model_config["sentence_transformer_model"]
            )
            self.logger.info("✅ Sentence Transformer loaded")
            
            # Initialize spaCy model
            try:
                self.spacy_model = spacy.load(self.model_config["spacy_model"])
            except OSError:
                self.logger.warning("⚠️ spaCy model not found, downloading...")
                spacy.cli.download(self.model_config["spacy_model"])
                self.spacy_model = spacy.load(self.model_config["spacy_model"])
            self.logger.info("✅ spaCy model loaded")
            
            # Initialize sentiment analysis pipeline
            self.sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model=self.model_config["sentiment_model"],
                device=0 if self.model_config["device"] == "cuda" else -1
            )
            self.logger.info("✅ Sentiment analysis pipeline loaded")
            
            # Initialize intent classifier (simplified for now)
            self.intent_classifier = self._create_hvac_intent_classifier()
            self.logger.info("✅ Intent classifier initialized")
            
            self.logger.info("🎉 Semantic Analyzer initialization complete!")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Semantic Analyzer: {e}")
            return False
    
    def _create_hvac_intent_classifier(self) -> Dict[str, List[str]]:
        """Create HVAC-specific intent classifier"""
        return {
            "service_request": [
                "repair", "fix", "broken", "not working", "maintenance", 
                "service", "check", "inspect", "clean"
            ],
            "emergency": [
                "emergency", "urgent", "immediately", "asap", "critical",
                "no heat", "no cooling", "flooding", "gas leak"
            ],
            "inquiry": [
                "question", "how", "what", "when", "where", "why",
                "information", "quote", "estimate", "price"
            ],
            "complaint": [
                "complaint", "dissatisfied", "unhappy", "problem",
                "issue", "wrong", "bad", "terrible", "awful"
            ],
            "appointment": [
                "schedule", "appointment", "book", "arrange",
                "visit", "come", "available", "time"
            ]
        }
    
    def _load_hvac_equipment_terms(self) -> List[str]:
        """Load HVAC equipment terminology"""
        return [
            # Air Conditioning
            "air conditioner", "ac unit", "central air", "split system",
            "window unit", "portable ac", "ductless", "mini split",
            
            # Heating
            "furnace", "boiler", "heat pump", "radiator", "baseboard",
            "radiant heating", "geothermal", "electric heat",
            
            # Ventilation
            "ventilation", "exhaust fan", "air handler", "ductwork",
            "air filter", "damper", "vents", "grilles",
            
            # Components
            "compressor", "condenser", "evaporator", "thermostat",
            "refrigerant", "coils", "blower", "motor", "fan",
            
            # Brands
            "lg", "daikin", "mitsubishi", "carrier", "trane",
            "lennox", "rheem", "goodman", "york", "american standard"
        ]
    
    def _load_hvac_issue_patterns(self) -> Dict[str, List[str]]:
        """Load HVAC issue patterns"""
        return {
            "cooling_issues": [
                "not cooling", "warm air", "no cold air", "ice buildup",
                "frozen coils", "refrigerant leak", "compressor noise"
            ],
            "heating_issues": [
                "not heating", "cold air", "no heat", "pilot light",
                "ignition problem", "gas smell", "furnace noise"
            ],
            "airflow_issues": [
                "weak airflow", "no air", "blocked vents", "dirty filter",
                "ductwork problem", "fan not working"
            ],
            "electrical_issues": [
                "won't turn on", "power issue", "breaker tripped",
                "electrical problem", "wiring issue", "short circuit"
            ],
            "noise_issues": [
                "loud noise", "grinding", "squealing", "rattling",
                "banging", "humming", "vibration"
            ]
        }
    
    def _load_urgency_indicators(self) -> Dict[str, float]:
        """Load urgency indicators with weights"""
        return {
            "emergency": 1.0,
            "urgent": 0.9,
            "asap": 0.9,
            "immediately": 0.9,
            "critical": 0.8,
            "no heat": 0.8,
            "no cooling": 0.8,
            "gas leak": 1.0,
            "flooding": 0.9,
            "electrical": 0.7,
            "safety": 0.8,
            "today": 0.6,
            "soon": 0.4,
            "when possible": 0.2
        }
    
    async def analyze_text(self, text: str) -> SemanticAnalysisResult:
        """
        🧠 Comprehensive semantic analysis of text
        """
        start_time = datetime.now()
        
        try:
            # Generate embeddings
            embeddings = await self._generate_embeddings(text)
            
            # Extract entities
            entities = await self._extract_entities(text)
            
            # Analyze sentiment
            sentiment = await self._analyze_sentiment(text)
            
            # Classify intent
            intent = await self._classify_intent(text)
            
            # Extract keywords
            keywords = await self._extract_keywords(text)
            
            # Identify HVAC concepts
            hvac_concepts = await self._identify_hvac_concepts(text)
            
            # Calculate urgency score
            urgency_score = await self._calculate_urgency_score(text)
            
            # Calculate overall confidence
            confidence = await self._calculate_confidence(
                sentiment, intent, len(entities), len(hvac_concepts)
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Update metrics
            self.analysis_count += 1
            self.total_processing_time += processing_time
            
            result = SemanticAnalysisResult(
                text=text,
                embeddings=embeddings,
                entities=entities,
                sentiment=sentiment,
                intent=intent,
                keywords=keywords,
                hvac_concepts=hvac_concepts,
                urgency_score=urgency_score,
                confidence=confidence,
                processing_time=processing_time
            )
            
            self.logger.info(f"✅ Semantic analysis completed in {processing_time:.3f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Semantic analysis failed: {e}")
            # Return empty result
            return SemanticAnalysisResult(
                text=text,
                embeddings=[],
                entities=[],
                sentiment={"label": "UNKNOWN", "score": 0.0},
                intent={"label": "unknown", "confidence": 0.0},
                keywords=[],
                hvac_concepts=[],
                urgency_score=0.0,
                confidence=0.0,
                processing_time=0.0
            )
    
    async def _generate_embeddings(self, text: str) -> List[float]:
        """Generate semantic embeddings"""
        try:
            if not self.sentence_transformer:
                return []
            
            embeddings = self.sentence_transformer.encode(text)
            return embeddings.tolist()
            
        except Exception as e:
            self.logger.error(f"Embedding generation failed: {e}")
            return []
    
    async def _extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """Extract named entities and HVAC-specific entities"""
        entities = []
        
        try:
            if self.spacy_model:
                doc = self.spacy_model(text)
                
                # Standard NER entities
                for ent in doc.ents:
                    entities.append({
                        "text": ent.text,
                        "label": ent.label_,
                        "start": ent.start_char,
                        "end": ent.end_char,
                        "confidence": 1.0  # spaCy doesn't provide confidence
                    })
                
                # HVAC equipment entities
                text_lower = text.lower()
                for equipment in self.hvac_equipment_terms:
                    if equipment in text_lower:
                        start_idx = text_lower.find(equipment)
                        entities.append({
                            "text": equipment,
                            "label": "HVAC_EQUIPMENT",
                            "start": start_idx,
                            "end": start_idx + len(equipment),
                            "confidence": 0.9
                        })
            
            return entities
            
        except Exception as e:
            self.logger.error(f"Entity extraction failed: {e}")
            return []
    
    async def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment with confidence scores"""
        try:
            if not self.sentiment_pipeline:
                return {"label": "UNKNOWN", "score": 0.0}
            
            result = self.sentiment_pipeline(text)[0]
            return {
                "label": result["label"],
                "score": result["score"],
                "polarity": self._map_sentiment_to_polarity(result["label"])
            }
            
        except Exception as e:
            self.logger.error(f"Sentiment analysis failed: {e}")
            return {"label": "UNKNOWN", "score": 0.0, "polarity": 0.0}
    
    def _map_sentiment_to_polarity(self, label: str) -> float:
        """Map sentiment label to polarity score"""
        mapping = {
            "POSITIVE": 1.0,
            "NEGATIVE": -1.0,
            "NEUTRAL": 0.0
        }
        return mapping.get(label.upper(), 0.0)
    
    async def _classify_intent(self, text: str) -> Dict[str, Any]:
        """Classify communication intent"""
        try:
            text_lower = text.lower()
            intent_scores = {}
            
            for intent, keywords in self.intent_classifier.items():
                score = 0.0
                matches = 0
                
                for keyword in keywords:
                    if keyword in text_lower:
                        score += 1.0
                        matches += 1
                
                if matches > 0:
                    intent_scores[intent] = score / len(keywords)
            
            if intent_scores:
                best_intent = max(intent_scores, key=intent_scores.get)
                confidence = intent_scores[best_intent]
                
                return {
                    "label": best_intent,
                    "confidence": confidence,
                    "all_scores": intent_scores
                }
            
            return {"label": "unknown", "confidence": 0.0, "all_scores": {}}
            
        except Exception as e:
            self.logger.error(f"Intent classification failed: {e}")
            return {"label": "unknown", "confidence": 0.0, "all_scores": {}}
    
    async def _extract_keywords(self, text: str) -> List[str]:
        """Extract important keywords"""
        try:
            if not self.spacy_model:
                return []
            
            doc = self.spacy_model(text)
            keywords = []
            
            # Extract important tokens (nouns, adjectives, verbs)
            for token in doc:
                if (token.pos_ in ["NOUN", "ADJ", "VERB"] and 
                    not token.is_stop and 
                    not token.is_punct and 
                    len(token.text) > 2):
                    keywords.append(token.lemma_.lower())
            
            # Remove duplicates and return
            return list(set(keywords))
            
        except Exception as e:
            self.logger.error(f"Keyword extraction failed: {e}")
            return []
    
    async def _identify_hvac_concepts(self, text: str) -> List[str]:
        """Identify HVAC-specific concepts"""
        concepts = []
        text_lower = text.lower()
        
        # Check for equipment mentions
        for equipment in self.hvac_equipment_terms:
            if equipment in text_lower:
                concepts.append(equipment)
        
        # Check for issue patterns
        for issue_type, patterns in self.hvac_issue_patterns.items():
            for pattern in patterns:
                if pattern in text_lower:
                    concepts.append(f"issue:{issue_type}")
                    break
        
        return list(set(concepts))
    
    async def _calculate_urgency_score(self, text: str) -> float:
        """Calculate urgency score based on indicators"""
        text_lower = text.lower()
        urgency_score = 0.0
        
        for indicator, weight in self.urgency_indicators.items():
            if indicator in text_lower:
                urgency_score = max(urgency_score, weight)
        
        return min(urgency_score, 1.0)
    
    async def _calculate_confidence(self, 
                                  sentiment: Dict[str, Any],
                                  intent: Dict[str, Any],
                                  entity_count: int,
                                  concept_count: int) -> float:
        """Calculate overall analysis confidence"""
        confidence_factors = []
        
        # Sentiment confidence
        if sentiment.get("score", 0) > 0:
            confidence_factors.append(sentiment["score"])
        
        # Intent confidence
        if intent.get("confidence", 0) > 0:
            confidence_factors.append(intent["confidence"])
        
        # Entity detection confidence
        if entity_count > 0:
            confidence_factors.append(min(entity_count / 5.0, 1.0))
        
        # HVAC concept confidence
        if concept_count > 0:
            confidence_factors.append(min(concept_count / 3.0, 1.0))
        
        if confidence_factors:
            return sum(confidence_factors) / len(confidence_factors)
        
        return 0.0
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        avg_processing_time = (
            self.total_processing_time / self.analysis_count 
            if self.analysis_count > 0 else 0.0
        )
        
        return {
            "total_analyses": self.analysis_count,
            "total_processing_time": self.total_processing_time,
            "average_processing_time": avg_processing_time,
            "analyses_per_second": (
                self.analysis_count / self.total_processing_time 
                if self.total_processing_time > 0 else 0.0
            )
        }