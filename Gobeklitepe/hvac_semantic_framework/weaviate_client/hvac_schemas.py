"""
🏗️ HVAC Domain-Specific Schemas for Weaviate
Advanced schema definitions optimized for HVAC operations
"""

from typing import Dict, List, Any
from dataclasses import dataclass

@dataclass
class HVACSchemas:
    """
    🔥 MOST COMPREHENSIVE HVAC schemas for semantic analysis
    
    Includes:
    - Equipment specifications and manuals
    - Customer communications and transcriptions
    - Service orders and maintenance records
    - Technical documentation and procedures
    - Business intelligence and analytics
    """
    
    @staticmethod
    def get_equipment_schema() -> Dict[str, Any]:
        """
        🔧 Equipment schema for LG, Daikin, and other HVAC equipment
        """
        return {
            "class": "HVACEquipment",
            "description": "HVAC equipment with specifications and semantic search",
            "vectorizer": "text2vec-transformers",
            "moduleConfig": {
                "text2vec-transformers": {
                    "poolingStrategy": "masked_mean",
                    "vectorizeClassName": True
                }
            },
            "properties": [
                {
                    "name": "equipmentId",
                    "dataType": ["text"],
                    "description": "Unique equipment identifier",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "brand",
                    "dataType": ["text"],
                    "description": "Equipment brand (L<PERSON>, <PERSON><PERSON>, Mitsubishi, etc.)",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "model",
                    "dataType": ["text"],
                    "description": "Equipment model number",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "type",
                    "dataType": ["text"],
                    "description": "Equipment type (air conditioner, heat pump, ventilation, etc.)",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "specifications",
                    "dataType": ["text"],
                    "description": "Technical specifications and features",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "capacity",
                    "dataType": ["number"],
                    "description": "Cooling/heating capacity in BTU or kW"
                },
                {
                    "name": "energyRating",
                    "dataType": ["text"],
                    "description": "Energy efficiency rating (SEER, EER, COP)"
                },
                {
                    "name": "installationDate",
                    "dataType": ["date"],
                    "description": "Date of installation"
                },
                {
                    "name": "warrantyInfo",
                    "dataType": ["text"],
                    "description": "Warranty information and terms"
                },
                {
                    "name": "manualContent",
                    "dataType": ["text"],
                    "description": "Equipment manual and documentation content",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "troubleshootingGuide",
                    "dataType": ["text"],
                    "description": "Troubleshooting procedures and solutions",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "maintenanceSchedule",
                    "dataType": ["text"],
                    "description": "Recommended maintenance schedule and procedures"
                },
                {
                    "name": "customerId",
                    "dataType": ["text"],
                    "description": "Associated customer ID"
                },
                {
                    "name": "location",
                    "dataType": ["text"],
                    "description": "Installation location details"
                },
                {
                    "name": "status",
                    "dataType": ["text"],
                    "description": "Current equipment status (active, maintenance, retired)"
                },
                {
                    "name": "lastServiceDate",
                    "dataType": ["date"],
                    "description": "Date of last service"
                },
                {
                    "name": "nextServiceDue",
                    "dataType": ["date"],
                    "description": "Next scheduled service date"
                },
                {
                    "name": "semanticTags",
                    "dataType": ["text[]"],
                    "description": "Semantic tags for enhanced search"
                }
            ]
        }
    
    @staticmethod
    def get_customer_communication_schema() -> Dict[str, Any]:
        """
        📧 Customer communication schema for emails and transcriptions
        """
        return {
            "class": "CustomerCommunication",
            "description": "Customer emails, transcriptions, and communication history",
            "vectorizer": "text2vec-transformers",
            "moduleConfig": {
                "text2vec-transformers": {
                    "poolingStrategy": "masked_mean",
                    "vectorizeClassName": True
                }
            },
            "properties": [
                {
                    "name": "communicationId",
                    "dataType": ["text"],
                    "description": "Unique communication identifier"
                },
                {
                    "name": "customerId",
                    "dataType": ["text"],
                    "description": "Associated customer ID"
                },
                {
                    "name": "type",
                    "dataType": ["text"],
                    "description": "Communication type (email, phone, transcription, chat)"
                },
                {
                    "name": "subject",
                    "dataType": ["text"],
                    "description": "Communication subject or title",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "content",
                    "dataType": ["text"],
                    "description": "Full communication content",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "transcriptionText",
                    "dataType": ["text"],
                    "description": "Transcribed audio content",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "sentiment",
                    "dataType": ["text"],
                    "description": "Sentiment analysis result (positive, negative, neutral)"
                },
                {
                    "name": "sentimentScore",
                    "dataType": ["number"],
                    "description": "Sentiment confidence score (0-1)"
                },
                {
                    "name": "intent",
                    "dataType": ["text"],
                    "description": "Communication intent (service_request, complaint, inquiry, etc.)"
                },
                {
                    "name": "urgency",
                    "dataType": ["text"],
                    "description": "Urgency level (low, medium, high, critical)"
                },
                {
                    "name": "urgencyScore",
                    "dataType": ["number"],
                    "description": "Urgency confidence score (0-1)"
                },
                {
                    "name": "keywords",
                    "dataType": ["text[]"],
                    "description": "Extracted keywords and entities"
                },
                {
                    "name": "equipmentMentioned",
                    "dataType": ["text[]"],
                    "description": "Equipment mentioned in communication"
                },
                {
                    "name": "issuesIdentified",
                    "dataType": ["text[]"],
                    "description": "Technical issues identified"
                },
                {
                    "name": "timestamp",
                    "dataType": ["date"],
                    "description": "Communication timestamp"
                },
                {
                    "name": "source",
                    "dataType": ["text"],
                    "description": "Communication source (dolores@, grzegorz@, etc.)"
                },
                {
                    "name": "processed",
                    "dataType": ["boolean"],
                    "description": "Whether communication has been processed"
                },
                {
                    "name": "responseGenerated",
                    "dataType": ["boolean"],
                    "description": "Whether automatic response was generated"
                },
                {
                    "name": "followUpRequired",
                    "dataType": ["boolean"],
                    "description": "Whether follow-up action is required"
                },
                {
                    "name": "semanticSummary",
                    "dataType": ["text"],
                    "description": "AI-generated semantic summary",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                }
            ]
        }
    
    @staticmethod
    def get_service_order_schema() -> Dict[str, Any]:
        """
        🔧 Service order schema for maintenance and repair tracking
        """
        return {
            "class": "ServiceOrder",
            "description": "HVAC service orders and maintenance records",
            "vectorizer": "text2vec-transformers",
            "moduleConfig": {
                "text2vec-transformers": {
                    "poolingStrategy": "masked_mean",
                    "vectorizeClassName": True
                }
            },
            "properties": [
                {
                    "name": "serviceOrderId",
                    "dataType": ["text"],
                    "description": "Unique service order identifier"
                },
                {
                    "name": "customerId",
                    "dataType": ["text"],
                    "description": "Associated customer ID"
                },
                {
                    "name": "equipmentId",
                    "dataType": ["text"],
                    "description": "Associated equipment ID"
                },
                {
                    "name": "serviceType",
                    "dataType": ["text"],
                    "description": "Type of service (maintenance, repair, installation, inspection)"
                },
                {
                    "name": "description",
                    "dataType": ["text"],
                    "description": "Service description and details",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "problemDescription",
                    "dataType": ["text"],
                    "description": "Customer-reported problem description",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "diagnosisResults",
                    "dataType": ["text"],
                    "description": "Technician diagnosis and findings",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "workPerformed",
                    "dataType": ["text"],
                    "description": "Detailed work performed",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "partsUsed",
                    "dataType": ["text[]"],
                    "description": "Parts and materials used"
                },
                {
                    "name": "technicianId",
                    "dataType": ["text"],
                    "description": "Assigned technician ID"
                },
                {
                    "name": "priority",
                    "dataType": ["text"],
                    "description": "Service priority (low, medium, high, emergency)"
                },
                {
                    "name": "status",
                    "dataType": ["text"],
                    "description": "Current status (scheduled, in_progress, completed, cancelled)"
                },
                {
                    "name": "scheduledDate",
                    "dataType": ["date"],
                    "description": "Scheduled service date"
                },
                {
                    "name": "completedDate",
                    "dataType": ["date"],
                    "description": "Service completion date"
                },
                {
                    "name": "duration",
                    "dataType": ["number"],
                    "description": "Service duration in hours"
                },
                {
                    "name": "cost",
                    "dataType": ["number"],
                    "description": "Total service cost"
                },
                {
                    "name": "customerSatisfaction",
                    "dataType": ["number"],
                    "description": "Customer satisfaction rating (1-5)"
                },
                {
                    "name": "followUpRequired",
                    "dataType": ["boolean"],
                    "description": "Whether follow-up service is required"
                },
                {
                    "name": "warrantyWork",
                    "dataType": ["boolean"],
                    "description": "Whether work is covered under warranty"
                },
                {
                    "name": "semanticTags",
                    "dataType": ["text[]"],
                    "description": "Semantic tags for enhanced search"
                }
            ]
        }
    
    @staticmethod
    def get_hvac_knowledge_schema() -> Dict[str, Any]:
        """
        📚 HVAC knowledge base schema for technical documentation
        """
        return {
            "class": "HVACKnowledge",
            "description": "HVAC technical knowledge base and documentation",
            "vectorizer": "text2vec-transformers",
            "moduleConfig": {
                "text2vec-transformers": {
                    "poolingStrategy": "masked_mean",
                    "vectorizeClassName": True
                }
            },
            "properties": [
                {
                    "name": "knowledgeId",
                    "dataType": ["text"],
                    "description": "Unique knowledge article identifier"
                },
                {
                    "name": "title",
                    "dataType": ["text"],
                    "description": "Knowledge article title",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "content",
                    "dataType": ["text"],
                    "description": "Full knowledge article content",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "category",
                    "dataType": ["text"],
                    "description": "Knowledge category (troubleshooting, installation, maintenance, etc.)"
                },
                {
                    "name": "subcategory",
                    "dataType": ["text"],
                    "description": "Knowledge subcategory"
                },
                {
                    "name": "equipmentTypes",
                    "dataType": ["text[]"],
                    "description": "Applicable equipment types"
                },
                {
                    "name": "brands",
                    "dataType": ["text[]"],
                    "description": "Applicable equipment brands"
                },
                {
                    "name": "difficulty",
                    "dataType": ["text"],
                    "description": "Difficulty level (beginner, intermediate, advanced, expert)"
                },
                {
                    "name": "estimatedTime",
                    "dataType": ["number"],
                    "description": "Estimated time to complete in hours"
                },
                {
                    "name": "toolsRequired",
                    "dataType": ["text[]"],
                    "description": "Required tools and equipment"
                },
                {
                    "name": "safetyNotes",
                    "dataType": ["text"],
                    "description": "Safety considerations and warnings"
                },
                {
                    "name": "stepByStepGuide",
                    "dataType": ["text"],
                    "description": "Detailed step-by-step instructions",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "commonIssues",
                    "dataType": ["text"],
                    "description": "Common issues and solutions",
                    "moduleConfig": {
                        "text2vec-transformers": {
                            "skip": False,
                            "vectorizePropertyName": True
                        }
                    }
                },
                {
                    "name": "relatedArticles",
                    "dataType": ["text[]"],
                    "description": "Related knowledge article IDs"
                },
                {
                    "name": "tags",
                    "dataType": ["text[]"],
                    "description": "Searchable tags"
                },
                {
                    "name": "author",
                    "dataType": ["text"],
                    "description": "Article author"
                },
                {
                    "name": "lastUpdated",
                    "dataType": ["date"],
                    "description": "Last update date"
                },
                {
                    "name": "version",
                    "dataType": ["text"],
                    "description": "Article version"
                },
                {
                    "name": "verified",
                    "dataType": ["boolean"],
                    "description": "Whether article has been verified by experts"
                }
            ]
        }
    
    @staticmethod
    def get_all_schemas() -> List[Dict[str, Any]]:
        """
        🚀 Get all HVAC schemas for complete setup
        """
        return [
            HVACSchemas.get_equipment_schema(),
            HVACSchemas.get_customer_communication_schema(),
            HVACSchemas.get_service_order_schema(),
            HVACSchemas.get_hvac_knowledge_schema()
        ]