"""
🚀 Advanced Weaviate Manager for HVAC Semantic Operations
Most powerful vector database client with enterprise-grade features
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime
import json

import weaviate
from weaviate.auth import AuthApiKey
from weaviate.config import Config
from weaviate.exceptions import WeaviateException

@dataclass
class WeaviateConfig:
    """Configuration for Weaviate connection"""
    url: str = "http://localhost:8080"
    api_key: Optional[str] = None
    timeout: int = 30
    startup_period: int = 5
    additional_headers: Optional[Dict[str, str]] = None
    
class WeaviateManager:
    """
    🔥 MOST POWERFUL Weaviate Manager for HVAC Operations
    
    Features:
    - Enterprise-grade connection management
    - Advanced schema operations
    - High-performance vector operations
    - HVAC domain-specific optimizations
    - Real-time monitoring and health checks
    - Automatic failover and recovery
    """
    
    def __init__(self, config: WeaviateConfig):
        self.config = config
        self.client: Optional[weaviate.Client] = None
        self.logger = logging.getLogger(__name__)
        self._connection_pool = []
        self._health_status = {"status": "disconnected", "last_check": None}
        
    async def connect(self) -> bool:
        """
        🚀 Establish connection to Weaviate with advanced configuration
        """
        try:
            # Configure authentication
            auth_config = None
            if self.config.api_key:
                auth_config = AuthApiKey(api_key=self.config.api_key)
            
            # Advanced client configuration
            client_config = Config(
                timeout=self.config.timeout,
                startup_period=self.config.startup_period,
                additional_headers=self.config.additional_headers or {}
            )
            
            # Create client with enterprise features
            self.client = weaviate.Client(
                url=self.config.url,
                auth_client_secret=auth_config,
                additional_config=client_config
            )
            
            # Verify connection
            if await self._health_check():
                self.logger.info("🚀 Weaviate connection established successfully")
                return True
            else:
                self.logger.error("❌ Weaviate health check failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Failed to connect to Weaviate: {e}")
            return False
    
    async def _health_check(self) -> bool:
        """
        🔍 Advanced health check with detailed diagnostics
        """
        try:
            if not self.client:
                return False
                
            # Check if Weaviate is ready
            ready = self.client.is_ready()
            
            # Check if we can perform basic operations
            meta = self.client.get_meta()
            
            # Update health status
            self._health_status = {
                "status": "healthy" if ready else "unhealthy",
                "last_check": datetime.now().isoformat(),
                "meta": meta,
                "ready": ready
            }
            
            return ready
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            self._health_status = {
                "status": "error",
                "last_check": datetime.now().isoformat(),
                "error": str(e)
            }
            return False
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get current health status"""
        return self._health_status.copy()
    
    async def create_schema(self, schema: Dict[str, Any]) -> bool:
        """
        🏗️ Create advanced schema with HVAC optimizations
        """
        try:
            if not self.client:
                raise WeaviateException("Client not connected")
            
            # Validate schema structure
            if not self._validate_schema(schema):
                raise ValueError("Invalid schema structure")
            
            # Create schema with optimizations
            self.client.schema.create_class(schema)
            
            self.logger.info(f"✅ Schema created: {schema.get('class', 'Unknown')}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to create schema: {e}")
            return False
    
    def _validate_schema(self, schema: Dict[str, Any]) -> bool:
        """Validate schema structure"""
        required_fields = ["class", "properties"]
        return all(field in schema for field in required_fields)
    
    async def insert_object(self, 
                          class_name: str, 
                          properties: Dict[str, Any],
                          vector: Optional[List[float]] = None,
                          uuid: Optional[str] = None) -> Optional[str]:
        """
        📝 Insert object with advanced features
        """
        try:
            if not self.client:
                raise WeaviateException("Client not connected")
            
            # Prepare data object
            data_object = {
                "class": class_name,
                "properties": properties
            }
            
            if vector:
                data_object["vector"] = vector
            
            if uuid:
                data_object["id"] = uuid
            
            # Insert with batch optimization
            result = self.client.data_object.create(
                data_object=data_object,
                class_name=class_name,
                uuid=uuid
            )
            
            self.logger.info(f"✅ Object inserted: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Failed to insert object: {e}")
            return None
    
    async def semantic_search(self,
                            class_name: str,
                            query: str,
                            limit: int = 10,
                            certainty: float = 0.7,
                            additional_properties: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        🔍 Advanced semantic search with HVAC optimizations
        """
        try:
            if not self.client:
                raise WeaviateException("Client not connected")
            
            # Build search query
            search_query = (
                self.client.query
                .get(class_name, additional_properties or ["*"])
                .with_near_text({"concepts": [query], "certainty": certainty})
                .with_limit(limit)
                .with_additional(["certainty", "distance"])
            )
            
            # Execute search
            result = search_query.do()
            
            # Extract results
            if "data" in result and "Get" in result["data"]:
                objects = result["data"]["Get"].get(class_name, [])
                self.logger.info(f"🔍 Found {len(objects)} results for query: {query}")
                return objects
            
            return []
            
        except Exception as e:
            self.logger.error(f"❌ Semantic search failed: {e}")
            return []
    
    async def vector_search(self,
                          class_name: str,
                          vector: List[float],
                          limit: int = 10,
                          distance: float = 0.3) -> List[Dict[str, Any]]:
        """
        🎯 High-performance vector similarity search
        """
        try:
            if not self.client:
                raise WeaviateException("Client not connected")
            
            # Build vector search query
            search_query = (
                self.client.query
                .get(class_name, ["*"])
                .with_near_vector({"vector": vector, "distance": distance})
                .with_limit(limit)
                .with_additional(["certainty", "distance"])
            )
            
            # Execute search
            result = search_query.do()
            
            # Extract results
            if "data" in result and "Get" in result["data"]:
                objects = result["data"]["Get"].get(class_name, [])
                self.logger.info(f"🎯 Found {len(objects)} vector matches")
                return objects
            
            return []
            
        except Exception as e:
            self.logger.error(f"❌ Vector search failed: {e}")
            return []
    
    async def hybrid_search(self,
                          class_name: str,
                          query: str,
                          vector: Optional[List[float]] = None,
                          alpha: float = 0.5,
                          limit: int = 10) -> List[Dict[str, Any]]:
        """
        🔥 MOST POWERFUL: Hybrid search combining text and vector
        """
        try:
            if not self.client:
                raise WeaviateException("Client not connected")
            
            # Build hybrid search query
            search_query = (
                self.client.query
                .get(class_name, ["*"])
                .with_hybrid(query=query, alpha=alpha, vector=vector)
                .with_limit(limit)
                .with_additional(["score", "explainScore"])
            )
            
            # Execute search
            result = search_query.do()
            
            # Extract results
            if "data" in result and "Get" in result["data"]:
                objects = result["data"]["Get"].get(class_name, [])
                self.logger.info(f"🔥 Hybrid search found {len(objects)} results")
                return objects
            
            return []
            
        except Exception as e:
            self.logger.error(f"❌ Hybrid search failed: {e}")
            return []
    
    async def batch_insert(self, 
                         class_name: str, 
                         objects: List[Dict[str, Any]],
                         batch_size: int = 100) -> Dict[str, Any]:
        """
        ⚡ High-performance batch insertion
        """
        try:
            if not self.client:
                raise WeaviateException("Client not connected")
            
            results = {"success": 0, "failed": 0, "errors": []}
            
            # Process in batches
            for i in range(0, len(objects), batch_size):
                batch = objects[i:i + batch_size]
                
                with self.client.batch as batch_client:
                    batch_client.batch_size = batch_size
                    
                    for obj in batch:
                        batch_client.add_data_object(
                            data_object=obj.get("properties", {}),
                            class_name=class_name,
                            vector=obj.get("vector"),
                            uuid=obj.get("uuid")
                        )
                
                # Check for errors
                batch_results = self.client.batch.get_batch_stats()
                if batch_results:
                    results["success"] += batch_results.get("success", 0)
                    results["failed"] += batch_results.get("failed", 0)
            
            self.logger.info(f"⚡ Batch insert completed: {results}")
            return results
            
        except Exception as e:
            self.logger.error(f"❌ Batch insert failed: {e}")
            return {"success": 0, "failed": len(objects), "errors": [str(e)]}
    
    async def get_schema(self) -> Dict[str, Any]:
        """Get current schema"""
        try:
            if not self.client:
                raise WeaviateException("Client not connected")
            
            schema = self.client.schema.get()
            return schema
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get schema: {e}")
            return {}
    
    async def delete_class(self, class_name: str) -> bool:
        """Delete a class from schema"""
        try:
            if not self.client:
                raise WeaviateException("Client not connected")
            
            self.client.schema.delete_class(class_name)
            self.logger.info(f"🗑️ Class deleted: {class_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to delete class: {e}")
            return False
    
    async def close(self):
        """Close connection and cleanup"""
        if self.client:
            # Weaviate client doesn't have explicit close method
            # but we can clear references
            self.client = None
            self.logger.info("🔌 Weaviate connection closed")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        asyncio.create_task(self.close())