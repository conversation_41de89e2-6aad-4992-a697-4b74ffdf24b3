"""
🚀 HVAC Semantic Framework - Powered by Weaviate
The most advanced semantic analysis framework for HVAC operations

This framework combines:
- Weaviate vector database for semantic search
- LangGraph for stateful agent orchestration  
- PydanticAI for type-safe agent development
- Microsoft Semantic Kernel for enterprise reasoning
- Advanced HVAC domain intelligence
"""

__version__ = "1.0.0"
__author__ = "HVAC CRM Team"
__description__ = "Advanced Semantic Framework for HVAC Operations"

# Core imports will be added as we build each module
# from .core import *
# from .agents import *
# from .semantic import *
# from .weaviate_client import *

__all__ = [
    "HVACSemanticFramework",
    "WeaviateManager", 
    "SemanticAnalyzer",
    "AgentOrchestrator",
    "HVACDomainKnowledge"
]