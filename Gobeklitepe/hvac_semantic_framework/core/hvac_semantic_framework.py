"""
🚀 HVAC Semantic Framework - Main Integration Class
The most powerful semantic analysis framework for HVAC operations
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime
import json

# Framework components
from ..weaviate_client.weaviate_manager import WeaviateManager, WeaviateConfig
from ..weaviate_client.hvac_schemas import HVACSchemas
from ..semantic.semantic_analyzer import SemanticAnalyzer, SemanticAnalysisResult
from ..agents.agent_orchestrator import AgentOrchestrator, AgentTask, AgentType, TaskPriority

@dataclass
class FrameworkConfig:
    """Main framework configuration"""
    # Weaviate configuration
    weaviate_url: str = "http://localhost:8080"
    weaviate_api_key: Optional[str] = None
    
    # Semantic analysis configuration
    semantic_models: Dict[str, str] = None
    
    # Agent configuration
    max_concurrent_agents: int = 10
    agent_timeout: int = 300
    
    # Integration configuration
    enable_real_time: bool = True
    enable_monitoring: bool = True
    enable_persistence: bool = True
    
    # HVAC specific configuration
    hvac_equipment_brands: List[str] = None
    hvac_service_types: List[str] = None
    
    def __post_init__(self):
        if self.semantic_models is None:
            self.semantic_models = {
                "sentence_transformer": "all-MiniLM-L6-v2",
                "spacy_model": "en_core_web_sm",
                "sentiment_model": "cardiffnlp/twitter-roberta-base-sentiment-latest"
            }
        
        if self.hvac_equipment_brands is None:
            self.hvac_equipment_brands = ["LG", "Daikin", "Mitsubishi", "Carrier", "Trane"]
        
        if self.hvac_service_types is None:
            self.hvac_service_types = ["installation", "maintenance", "repair", "inspection"]

class HVACSemanticFramework:
    """
    🔥 MOST POWERFUL HVAC Semantic Framework
    
    Combines:
    - Weaviate vector database for semantic search
    - Advanced semantic analysis with domain expertise
    - Multi-agent orchestration with LangGraph/PydanticAI
    - Real-time processing and monitoring
    - HVAC domain-specific intelligence
    
    Features:
    - Equipment semantic understanding
    - Customer communication analysis
    - Predictive maintenance insights
    - Autonomous agent workflows
    - Real-time semantic search
    - Advanced analytics and reporting
    """
    
    def __init__(self, config: FrameworkConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Core components
        self.weaviate_manager: Optional[WeaviateManager] = None
        self.semantic_analyzer: Optional[SemanticAnalyzer] = None
        self.agent_orchestrator: Optional[AgentOrchestrator] = None
        
        # Framework state
        self.is_initialized = False
        self.start_time: Optional[datetime] = None
        
        # Performance metrics
        self.metrics = {
            "total_analyses": 0,
            "total_searches": 0,
            "total_agent_tasks": 0,
            "average_response_time": 0.0,
            "success_rate": 0.0
        }
    
    async def initialize(self) -> bool:
        """
        🚀 Initialize the complete HVAC Semantic Framework
        """
        try:
            self.logger.info("🚀 Initializing HVAC Semantic Framework...")
            self.start_time = datetime.now()
            
            # Initialize Weaviate
            await self._initialize_weaviate()
            self.logger.info("✅ Weaviate initialized")
            
            # Initialize Semantic Analyzer
            await self._initialize_semantic_analyzer()
            self.logger.info("✅ Semantic Analyzer initialized")
            
            # Initialize Agent Orchestrator
            await self._initialize_agent_orchestrator()
            self.logger.info("✅ Agent Orchestrator initialized")
            
            # Setup HVAC schemas
            await self._setup_hvac_schemas()
            self.logger.info("✅ HVAC schemas configured")
            
            self.is_initialized = True
            self.logger.info("🎉 HVAC Semantic Framework initialization complete!")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Framework initialization failed: {e}")
            return False
    
    async def _initialize_weaviate(self):
        """Initialize Weaviate vector database"""
        weaviate_config = WeaviateConfig(
            url=self.config.weaviate_url,
            api_key=self.config.weaviate_api_key
        )
        
        self.weaviate_manager = WeaviateManager(weaviate_config)
        
        if not await self.weaviate_manager.connect():
            raise Exception("Failed to connect to Weaviate")
    
    async def _initialize_semantic_analyzer(self):
        """Initialize semantic analysis engine"""
        semantic_config = {
            "sentence_transformer_model": self.config.semantic_models["sentence_transformer"],
            "spacy_model": self.config.semantic_models["spacy_model"],
            "sentiment_model": self.config.semantic_models["sentiment_model"]
        }
        
        self.semantic_analyzer = SemanticAnalyzer(semantic_config)
        
        if not await self.semantic_analyzer.initialize():
            raise Exception("Failed to initialize Semantic Analyzer")
    
    async def _initialize_agent_orchestrator(self):
        """Initialize agent orchestration system"""
        agent_config = {
            "max_concurrent_tasks": self.config.max_concurrent_agents,
            "task_timeout": self.config.agent_timeout,
            "enable_monitoring": self.config.enable_monitoring,
            "enable_persistence": self.config.enable_persistence
        }
        
        self.agent_orchestrator = AgentOrchestrator(agent_config)
        
        if not await self.agent_orchestrator.initialize():
            raise Exception("Failed to initialize Agent Orchestrator")
    
    async def _setup_hvac_schemas(self):
        """Setup HVAC-specific schemas in Weaviate"""
        schemas = HVACSchemas.get_all_schemas()
        
        for schema in schemas:
            await self.weaviate_manager.create_schema(schema)
    
    async def analyze_customer_communication(self, 
                                           communication_text: str,
                                           customer_id: Optional[str] = None,
                                           communication_type: str = "email") -> Dict[str, Any]:
        """
        📧 Analyze customer communication with full semantic intelligence
        """
        try:
            if not self.is_initialized:
                raise Exception("Framework not initialized")
            
            start_time = datetime.now()
            
            # Perform semantic analysis
            semantic_result = await self.semantic_analyzer.analyze_text(communication_text)
            
            # Store in Weaviate
            communication_data = {
                "communicationId": f"comm_{datetime.now().timestamp()}",
                "customerId": customer_id or "unknown",
                "type": communication_type,
                "content": communication_text,
                "transcriptionText": communication_text if communication_type == "transcription" else "",
                "sentiment": semantic_result.sentiment.get("label", "UNKNOWN"),
                "sentimentScore": semantic_result.sentiment.get("score", 0.0),
                "intent": semantic_result.intent.get("label", "unknown"),
                "urgency": self._classify_urgency(semantic_result.urgency_score),
                "urgencyScore": semantic_result.urgency_score,
                "keywords": semantic_result.keywords,
                "equipmentMentioned": [concept for concept in semantic_result.hvac_concepts if not concept.startswith("issue:")],
                "issuesIdentified": [concept.replace("issue:", "") for concept in semantic_result.hvac_concepts if concept.startswith("issue:")],
                "timestamp": datetime.now().isoformat(),
                "source": "framework_analysis",
                "processed": True,
                "semanticSummary": self._generate_semantic_summary(semantic_result)
            }
            
            # Insert into Weaviate
            result_id = await self.weaviate_manager.insert_object(
                class_name="CustomerCommunication",
                properties=communication_data,
                vector=semantic_result.embeddings
            )
            
            # Create agent task for follow-up processing
            if semantic_result.urgency_score > 0.7:  # High urgency
                agent_task = AgentTask(
                    agent_type=AgentType.CUSTOMER_SERVICE,
                    priority=TaskPriority.HIGH if semantic_result.urgency_score > 0.9 else TaskPriority.MEDIUM,
                    description=f"Process high-urgency customer communication",
                    input_data={
                        "communication_id": result_id,
                        "semantic_analysis": semantic_result.__dict__,
                        "customer_id": customer_id
                    }
                )
                
                task_id = await self.agent_orchestrator.submit_task(agent_task)
                communication_data["agent_task_id"] = task_id
            
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_metrics("analysis", processing_time, True)
            
            return {
                "success": True,
                "result_id": result_id,
                "semantic_analysis": semantic_result.__dict__,
                "communication_data": communication_data,
                "processing_time": processing_time
            }
            
        except Exception as e:
            self.logger.error(f"❌ Communication analysis failed: {e}")
            self._update_metrics("analysis", 0, False)
            return {
                "success": False,
                "error": str(e),
                "processing_time": 0
            }
    
    async def search_hvac_knowledge(self, 
                                  query: str,
                                  search_type: str = "hybrid",
                                  limit: int = 10) -> Dict[str, Any]:
        """
        🔍 Search HVAC knowledge base with semantic intelligence
        """
        try:
            if not self.is_initialized:
                raise Exception("Framework not initialized")
            
            start_time = datetime.now()
            
            # Generate query embeddings
            semantic_result = await self.semantic_analyzer.analyze_text(query)
            
            # Perform search based on type
            if search_type == "semantic":
                results = await self.weaviate_manager.semantic_search(
                    class_name="HVACKnowledge",
                    query=query,
                    limit=limit
                )
            elif search_type == "vector":
                results = await self.weaviate_manager.vector_search(
                    class_name="HVACKnowledge",
                    vector=semantic_result.embeddings,
                    limit=limit
                )
            else:  # hybrid
                results = await self.weaviate_manager.hybrid_search(
                    class_name="HVACKnowledge",
                    query=query,
                    vector=semantic_result.embeddings,
                    limit=limit
                )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_metrics("search", processing_time, True)
            
            return {
                "success": True,
                "query": query,
                "search_type": search_type,
                "results": results,
                "result_count": len(results),
                "semantic_analysis": semantic_result.__dict__,
                "processing_time": processing_time
            }
            
        except Exception as e:
            self.logger.error(f"❌ Knowledge search failed: {e}")
            self._update_metrics("search", 0, False)
            return {
                "success": False,
                "error": str(e),
                "processing_time": 0
            }
    
    async def process_equipment_data(self, 
                                   equipment_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔧 Process and store equipment data with semantic enhancement
        """
        try:
            if not self.is_initialized:
                raise Exception("Framework not initialized")
            
            start_time = datetime.now()
            
            # Create semantic description
            description_text = f"{equipment_info.get('brand', '')} {equipment_info.get('model', '')} {equipment_info.get('type', '')} {equipment_info.get('specifications', '')}"
            
            # Analyze semantically
            semantic_result = await self.semantic_analyzer.analyze_text(description_text)
            
            # Enhance equipment data
            enhanced_data = {
                **equipment_info,
                "semanticTags": semantic_result.keywords + semantic_result.hvac_concepts,
                "lastUpdated": datetime.now().isoformat()
            }
            
            # Insert into Weaviate
            result_id = await self.weaviate_manager.insert_object(
                class_name="HVACEquipment",
                properties=enhanced_data,
                vector=semantic_result.embeddings
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_metrics("equipment_processing", processing_time, True)
            
            return {
                "success": True,
                "result_id": result_id,
                "enhanced_data": enhanced_data,
                "semantic_analysis": semantic_result.__dict__,
                "processing_time": processing_time
            }
            
        except Exception as e:
            self.logger.error(f"❌ Equipment processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": 0
            }
    
    async def create_service_workflow(self, 
                                    service_request: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔄 Create intelligent service workflow with agent orchestration
        """
        try:
            if not self.is_initialized:
                raise Exception("Framework not initialized")
            
            # Analyze service request
            request_text = service_request.get("description", "")
            semantic_result = await self.semantic_analyzer.analyze_text(request_text)
            
            # Determine workflow based on analysis
            workflow_tasks = []
            
            # Always start with customer service
            workflow_tasks.append(AgentTask(
                agent_type=AgentType.CUSTOMER_SERVICE,
                priority=TaskPriority.HIGH if semantic_result.urgency_score > 0.7 else TaskPriority.MEDIUM,
                description="Analyze customer service request",
                input_data={"service_request": service_request, "semantic_analysis": semantic_result.__dict__}
            ))
            
            # Add equipment specialist if equipment mentioned
            if semantic_result.hvac_concepts:
                workflow_tasks.append(AgentTask(
                    agent_type=AgentType.EQUIPMENT_SPECIALIST,
                    priority=TaskPriority.MEDIUM,
                    description="Analyze equipment requirements",
                    input_data={"equipment_concepts": semantic_result.hvac_concepts}
                ))
            
            # Add technical support for issues
            issue_concepts = [c for c in semantic_result.hvac_concepts if c.startswith("issue:")]
            if issue_concepts:
                workflow_tasks.append(AgentTask(
                    agent_type=AgentType.TECHNICAL_SUPPORT,
                    priority=TaskPriority.HIGH,
                    description="Provide technical diagnosis and support",
                    input_data={"issues": issue_concepts}
                ))
            
            # Add maintenance planner
            workflow_tasks.append(AgentTask(
                agent_type=AgentType.MAINTENANCE_PLANNER,
                priority=TaskPriority.MEDIUM,
                description="Create maintenance schedule",
                input_data={"service_type": service_request.get("type", "general")}
            ))
            
            # Submit tasks to orchestrator
            task_ids = []
            for task in workflow_tasks:
                task_id = await self.agent_orchestrator.submit_task(task)
                task_ids.append(task_id)
            
            self._update_metrics("workflow", 0, True)
            
            return {
                "success": True,
                "workflow_id": f"workflow_{datetime.now().timestamp()}",
                "task_ids": task_ids,
                "semantic_analysis": semantic_result.__dict__,
                "estimated_completion": "2-4 hours"
            }
            
        except Exception as e:
            self.logger.error(f"❌ Service workflow creation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _classify_urgency(self, urgency_score: float) -> str:
        """Classify urgency level"""
        if urgency_score >= 0.9:
            return "critical"
        elif urgency_score >= 0.7:
            return "high"
        elif urgency_score >= 0.4:
            return "medium"
        else:
            return "low"
    
    def _generate_semantic_summary(self, semantic_result: SemanticAnalysisResult) -> str:
        """Generate semantic summary"""
        summary_parts = []
        
        if semantic_result.sentiment.get("label"):
            summary_parts.append(f"Sentiment: {semantic_result.sentiment['label']}")
        
        if semantic_result.intent.get("label"):
            summary_parts.append(f"Intent: {semantic_result.intent['label']}")
        
        if semantic_result.hvac_concepts:
            summary_parts.append(f"HVAC Concepts: {', '.join(semantic_result.hvac_concepts[:3])}")
        
        if semantic_result.urgency_score > 0.5:
            summary_parts.append(f"Urgency: {self._classify_urgency(semantic_result.urgency_score)}")
        
        return " | ".join(summary_parts)
    
    def _update_metrics(self, operation_type: str, processing_time: float, success: bool):
        """Update performance metrics"""
        if operation_type == "analysis":
            self.metrics["total_analyses"] += 1
        elif operation_type == "search":
            self.metrics["total_searches"] += 1
        elif operation_type == "workflow":
            self.metrics["total_agent_tasks"] += 1
        
        # Update average response time
        total_operations = sum([
            self.metrics["total_analyses"],
            self.metrics["total_searches"], 
            self.metrics["total_agent_tasks"]
        ])
        
        if total_operations > 0:
            current_avg = self.metrics["average_response_time"]
            self.metrics["average_response_time"] = (
                (current_avg * (total_operations - 1) + processing_time) / total_operations
            )
        
        # Update success rate
        if success:
            successful_operations = total_operations  # Simplified for now
            self.metrics["success_rate"] = successful_operations / total_operations
    
    def get_framework_status(self) -> Dict[str, Any]:
        """Get comprehensive framework status"""
        uptime = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        
        status = {
            "initialized": self.is_initialized,
            "uptime_seconds": uptime,
            "components": {
                "weaviate": self.weaviate_manager.get_health_status() if self.weaviate_manager else {"status": "not_initialized"},
                "semantic_analyzer": {"status": "initialized" if self.semantic_analyzer else "not_initialized"},
                "agent_orchestrator": self.agent_orchestrator.get_status() if self.agent_orchestrator else {"status": "not_initialized"}
            },
            "metrics": self.metrics
        }
        
        return status
    
    async def shutdown(self):
        """Gracefully shutdown the framework"""
        self.logger.info("🔌 Shutting down HVAC Semantic Framework...")
        
        if self.weaviate_manager:
            await self.weaviate_manager.close()
        
        self.is_initialized = False
        self.logger.info("✅ Framework shutdown complete")