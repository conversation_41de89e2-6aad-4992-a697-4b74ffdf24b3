{"schema": 1, "description": "Generate a list of chapter synopsis for a novel or novella", "execution_settings": {"default": {"max_tokens": 2048, "temperature": 0.1, "top_p": 0.5, "presence_penalty": 0.0, "frequency_penalty": 0.0}}, "input_variables": [{"name": "input", "description": "What the novel should be about.", "default": ""}, {"name": "chapterCount", "description": "The number of chapters to generate.", "default": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "The marker to use to end each chapter.", "default": "<!--===ENDPART===-->"}]}