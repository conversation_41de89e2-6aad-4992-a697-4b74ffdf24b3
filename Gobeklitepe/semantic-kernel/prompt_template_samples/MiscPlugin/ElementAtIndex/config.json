{"schema": 1, "description": "Get an element from an array or list at a specified index", "execution_settings": {"default": {"max_tokens": 1024, "temperature": 0.0, "top_p": 0.0, "presence_penalty": 0.0, "frequency_penalty": 0.0}}, "input_variables": [{"name": "input", "description": "The input array or list", "default": ""}, {"name": "index", "description": "The index of the element to retrieve", "default": ""}, {"name": "count", "description": "The number of items in the input", "default": ""}]}