# About Semantic Kernel

**Semantic Kernel (SK)** is a lightweight SDK enabling integration of AI Large
Language Models (LLMs) with conventional programming languages. The SK
extensible programming model combines natural language **semantic functions**,
traditional code **native functions**, and **embeddings-based memory** unlocking
new potential and adding value to applications with AI.

Semantic Kernel incorporates cutting-edge design patterns from the latest in AI
research. This enables developers to augment their applications with advanced
capabilities, such as prompt engineering, prompt chaining, retrieval-augmented
generation, contextual and long-term vectorized memory, embeddings,
summarization, zero or few-shot learning, semantic indexing, recursive
reasoning, intelligent planning, and access to external knowledge stores and
proprietary data.

# Getting Started ⚡

- Learn more at the [documentation site](https://aka.ms/SK-Docs).
- Join the [Discord community](https://aka.ms/SKDiscord).
- Follow the team on [Semantic Kernel blog](https://aka.ms/sk/blog).
- Check out the [GitHub repository](https://github.com/microsoft/semantic-kernel) for the latest updates.
