﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- THIS PROPERTY GROUP MUST COME FIRST -->
    <AssemblyName>Microsoft.SemanticKernel.Agents.AzureAI</AssemblyName>
    <RootNamespace>Microsoft.SemanticKernel.Agents.AzureAI</RootNamespace>
    <TargetFrameworks>net8.0;netstandard2.0</TargetFrameworks>
    <NoWarn>$(NoWarn);SKEXP0110</NoWarn>
    <EnablePackageValidation>false</EnablePackageValidation>
    <VersionSuffix>preview</VersionSuffix>
  </PropertyGroup>

  <Import Project="$(RepoRoot)/dotnet/nuget/nuget-package.props" />

  <PropertyGroup>
    <!-- NuGet Package Settings -->
    <Title>Semantic Kernel Agents - AzureAI</Title>
    <Description>Defines a concrete Agent based on the Azure AI Agent API.</Description>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="$(RepoRoot)/dotnet/src/InternalUtilities/azure/Policies/GeneratedActionPipelinePolicy.cs" Link="%(RecursiveDir)Azure/%(Filename)%(Extension)" />
    <Compile Include="$(RepoRoot)/dotnet/src/InternalUtilities/planning/Schema/JsonSchemaFunctionParameters.cs" Link="%(RecursiveDir)Schema/%(Filename)%(Extension)" />
    <Compile Include="$(RepoRoot)/dotnet/src/InternalUtilities/src/Diagnostics/*" Link="%(RecursiveDir)Utilities/%(Filename)%(Extension)" />
    <Compile Include="$(RepoRoot)/dotnet/src/InternalUtilities/src/Http/*" Link="%(RecursiveDir)Http/%(Filename)%(Extension)" />
    <Compile Include="$(RepoRoot)/dotnet/src/InternalUtilities/src/Text/*" Link="%(RecursiveDir)Text/%(Filename)%(Extension)" />
    <Compile Include="$(RepoRoot)/dotnet/src/InternalUtilities/src/System/IListExtensions.cs" Link="%(RecursiveDir)System/%(Filename)%(Extension)" />
    <Compile Include="$(RepoRoot)/dotnet/src/InternalUtilities/src/System/AppContextSwitchHelper.cs" Link="%(RecursiveDir)Utilities/%(Filename)%(Extension)" />
    <Compile Include="$(RepoRoot)/dotnet/src/InternalUtilities/src/Functions/FunctionName.cs" Link="%(RecursiveDir)Utilities/%(Filename)%(Extension)" />
    <Compile Include="$(RepoRoot)/dotnet/src/InternalUtilities/connectors/AI/**/*.cs" Link="%(RecursiveDir)%(Filename)%(Extension)" />
  </ItemGroup>

  <Import Project="$(RepoRoot)/dotnet/src/InternalUtilities/agents/AgentUtilities.props" />

  <ItemGroup>
    <ProjectReference Include="..\Abstractions\Agents.Abstractions.csproj" />
    <ProjectReference Include="..\..\SemanticKernel.Core\SemanticKernel.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.AI.Agents.Persistent" /> 
    <PackageReference Include="Azure.Identity" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="SemanticKernel.Agents.UnitTests" />
    <InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
  </ItemGroup>

</Project>