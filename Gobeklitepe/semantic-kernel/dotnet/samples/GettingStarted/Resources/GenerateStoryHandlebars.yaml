﻿name: GenerateStory
template: |
  Tell a story about {{topic}} that is {{length}} sentences long.
template_format: handlebars
description: A function that generates a story about a topic.
input_variables:
  - name: topic
    description: The topic of the story.
    is_required: true
  - name: length
    description: The number of sentences in the story.
    is_required: true
output_variable:
  description: The generated story.
execution_settings:
  service1:  
    model_id: gpt-4
    temperature: 0.6
  service2:
    model_id: gpt-3
    temperature: 0.4
  default:
    temperature: 0.5
