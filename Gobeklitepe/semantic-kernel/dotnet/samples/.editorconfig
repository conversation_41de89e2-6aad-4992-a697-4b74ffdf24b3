# Setting errors for SDK projects under samples folder
[*.cs]
indent_style = space
indent_size = 4
dotnet_diagnostic.CA2007.severity = error # Do not directly await a Task
dotnet_diagnostic.VSTHRD111.severity = error # Use .ConfigureAwait(bool)
dotnet_diagnostic.IDE1006.severity = error # Naming rule violations
dotnet_diagnostic.RCS1110.severity = none # Declare type inside namespace
dotnet_diagnostic.CA2201.severity = none # Exception is not sufficiently specific
dotnet_diagnostic.CS1998.severity = none # Async method lacks 'await' operators and will run synchronously
dotnet_diagnostic.CA1851.severity = none # Possible multiple enumerations of 'IEnumerable' collection
dotnet_diagnostic.CA1819.severity = none # Properties should not return arrays
dotnet_diagnostic.CA1812.severity = none # Avoid uninstantiated internal classes
dotnet_diagnostic.VSTHRD002.severity = none # Avoid problematic synchronous waits
dotnet_diagnostic.CS1587.severity = none # XML comment is not placed on a valid language element
dotnet_diagnostic.CA1031.severity = none # Do not catch general exception types
dotnet_diagnostic.CA2000.severity = none # Dispose objects before losing scope
dotnet_diagnostic.RCS1110.severity = none # Declare type inside namespace
dotnet_diagnostic.CA5394.severity = none # Do not use insecure randomness

# Resharper disabled rules: https://www.jetbrains.com/help/resharper/Reference__Code_Inspections_CSHARP.html#CodeSmell
resharper_condition_is_always_true_or_false_according_to_nullable_api_contract_highlighting = none # ConditionIsAlwaysTrueOrFalseAccordingToNullableAPIContract
resharper_inconsistent_naming_highlighting = none # InconsistentNaming
resharper_equal_expression_comparison_highlighting = none # EqualExpressionComparison
resharper_check_namespace_highlighting = none # CheckNamespace
resharper_arrange_object_creation_when_type_not_evident_highlighting = none # Disable "Arrange object creation when type is not evident" highlighting
resharper_arrange_this_qualifier_highlighting = none # Disable "Arrange 'this.' qualifier" highlighting