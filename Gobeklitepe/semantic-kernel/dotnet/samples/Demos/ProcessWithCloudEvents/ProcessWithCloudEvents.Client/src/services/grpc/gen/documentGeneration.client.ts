// @generated by protobuf-ts 2.9.6 with parameter generate_dependencies
// @generated from protobuf file "documentGeneration.proto" (syntax proto3)
// tslint:disable
import type { RpcTransport } from "@protobuf-ts/runtime-rpc";
import type { ServiceInfo } from "@protobuf-ts/runtime-rpc";
import { GrpcDocumentationGeneration } from "./documentGeneration";
import type { DocumentationApprovalRequest } from "./documentGeneration";
import type { ServerStreamingCall } from "@protobuf-ts/runtime-rpc";
import type { Empty } from "./documentGeneration";
import type { DocumentationContentRequest } from "./documentGeneration";
import { stackIntercept } from "@protobuf-ts/runtime-rpc";
import type { ProcessData } from "./documentGeneration";
import type { FeatureDocumentationRequest } from "./documentGeneration";
import type { UnaryCall } from "@protobuf-ts/runtime-rpc";
import type { RpcOptions } from "@protobuf-ts/runtime-rpc";
/**
 * @generated from protobuf service GrpcDocumentationGeneration
 */
export interface IGrpcDocumentationGenerationClient {
    /**
     * @generated from protobuf rpc: UserRequestFeatureDocumentation(FeatureDocumentationRequest) returns (ProcessData);
     */
    userRequestFeatureDocumentation(input: FeatureDocumentationRequest, options?: RpcOptions): UnaryCall<FeatureDocumentationRequest, ProcessData>;
    /**
     * @generated from protobuf rpc: RequestUserReviewDocumentationFromProcess(DocumentationContentRequest) returns (Empty);
     */
    requestUserReviewDocumentationFromProcess(input: DocumentationContentRequest, options?: RpcOptions): UnaryCall<DocumentationContentRequest, Empty>;
    /**
     * @generated from protobuf rpc: RequestUserReviewDocumentation(ProcessData) returns (stream DocumentationContentRequest);
     */
    requestUserReviewDocumentation(input: ProcessData, options?: RpcOptions): ServerStreamingCall<ProcessData, DocumentationContentRequest>;
    /**
     * @generated from protobuf rpc: UserReviewedDocumentation(DocumentationApprovalRequest) returns (Empty);
     */
    userReviewedDocumentation(input: DocumentationApprovalRequest, options?: RpcOptions): UnaryCall<DocumentationApprovalRequest, Empty>;
    /**
     * @generated from protobuf rpc: PublishDocumentation(DocumentationContentRequest) returns (Empty);
     */
    publishDocumentation(input: DocumentationContentRequest, options?: RpcOptions): UnaryCall<DocumentationContentRequest, Empty>;
    /**
     * @generated from protobuf rpc: ReceivePublishedDocumentation(ProcessData) returns (stream DocumentationContentRequest);
     */
    receivePublishedDocumentation(input: ProcessData, options?: RpcOptions): ServerStreamingCall<ProcessData, DocumentationContentRequest>;
}
/**
 * @generated from protobuf service GrpcDocumentationGeneration
 */
export class GrpcDocumentationGenerationClient implements IGrpcDocumentationGenerationClient, ServiceInfo {
    typeName = GrpcDocumentationGeneration.typeName;
    methods = GrpcDocumentationGeneration.methods;
    options = GrpcDocumentationGeneration.options;
    constructor(private readonly _transport: RpcTransport) {
    }
    /**
     * @generated from protobuf rpc: UserRequestFeatureDocumentation(FeatureDocumentationRequest) returns (ProcessData);
     */
    userRequestFeatureDocumentation(input: FeatureDocumentationRequest, options?: RpcOptions): UnaryCall<FeatureDocumentationRequest, ProcessData> {
        const method = this.methods[0], opt = this._transport.mergeOptions(options);
        return stackIntercept<FeatureDocumentationRequest, ProcessData>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: RequestUserReviewDocumentationFromProcess(DocumentationContentRequest) returns (Empty);
     */
    requestUserReviewDocumentationFromProcess(input: DocumentationContentRequest, options?: RpcOptions): UnaryCall<DocumentationContentRequest, Empty> {
        const method = this.methods[1], opt = this._transport.mergeOptions(options);
        return stackIntercept<DocumentationContentRequest, Empty>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: RequestUserReviewDocumentation(ProcessData) returns (stream DocumentationContentRequest);
     */
    requestUserReviewDocumentation(input: ProcessData, options?: RpcOptions): ServerStreamingCall<ProcessData, DocumentationContentRequest> {
        const method = this.methods[2], opt = this._transport.mergeOptions(options);
        return stackIntercept<ProcessData, DocumentationContentRequest>("serverStreaming", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: UserReviewedDocumentation(DocumentationApprovalRequest) returns (Empty);
     */
    userReviewedDocumentation(input: DocumentationApprovalRequest, options?: RpcOptions): UnaryCall<DocumentationApprovalRequest, Empty> {
        const method = this.methods[3], opt = this._transport.mergeOptions(options);
        return stackIntercept<DocumentationApprovalRequest, Empty>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: PublishDocumentation(DocumentationContentRequest) returns (Empty);
     */
    publishDocumentation(input: DocumentationContentRequest, options?: RpcOptions): UnaryCall<DocumentationContentRequest, Empty> {
        const method = this.methods[4], opt = this._transport.mergeOptions(options);
        return stackIntercept<DocumentationContentRequest, Empty>("unary", this._transport, method, opt, input);
    }
    /**
     * @generated from protobuf rpc: ReceivePublishedDocumentation(ProcessData) returns (stream DocumentationContentRequest);
     */
    receivePublishedDocumentation(input: ProcessData, options?: RpcOptions): ServerStreamingCall<ProcessData, DocumentationContentRequest> {
        const method = this.methods[5], opt = this._transport.mergeOptions(options);
        return stackIntercept<ProcessData, DocumentationContentRequest>("serverStreaming", this._transport, method, opt, input);
    }
}
