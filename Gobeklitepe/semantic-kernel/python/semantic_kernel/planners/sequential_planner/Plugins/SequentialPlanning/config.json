{"schema": 1, "description": "Given a request or command or goal generate a step by step plan to fulfill the request using functions. This ability is also known as decision making and function flow", "type": "completion", "execution_settings": {"default": {"max_tokens": 1024, "temperature": 0, "top_p": 0, "presence_penalty": 0, "frequency_penalty": 0, "stop_sequences": ["<!-- END -->"]}}, "input_variables": [{"name": "input", "description": "The question to answer", "defaultValue": ""}, {"name": "available_functions", "description": "The list of the agent's available_functions", "defaultValue": ""}]}