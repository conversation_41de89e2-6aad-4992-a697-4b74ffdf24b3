{"openapi": "3.0.1", "info": {"title": "Light Bulb API", "version": "v1"}, "servers": [{"url": "https://127.0.0.1"}], "paths": {"/Lights/{id}": {"get": {"operationId": "GetLightById", "tags": ["Lights"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}, "put": {"operationId": "PutLightById", "tags": ["Lights"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeStateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangeStateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangeStateRequest"}}}}, "responses": {"200": {"description": "Success"}}}, "delete": {"operationId": "DeleteLightById", "tags": ["Lights"], "parameters": [{"name": "id", "in": "path", "required": true, "style": "simple", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}}, "/Lights": {"get": {"operationId": "GetLights", "tags": ["Lights"], "parameters": [{"name": "roomId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}}}, "post": {"operationId": "CreateLights", "tags": ["Lights"], "parameters": [{"name": "roomId", "in": "query", "style": "form", "schema": {"type": "string", "format": "uuid"}}, {"name": "lightName", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"ChangeStateRequest": {"type": "object", "properties": {"isOn": {"type": "boolean", "description": "Specifies whether the light is turned on or off."}, "hexColor": {"type": "string", "description": "The hex color code for the light.", "nullable": true}, "brightness": {"enum": ["Low", "Medium", "High"], "type": "string", "description": "The brightness level of the light."}, "fadeDurationInMilliseconds": {"type": "integer", "description": "Duration for the light to fade to the new state, in milliseconds.", "format": "int32"}, "scheduledTime": {"type": "string", "description": "The time at which the change should occur.", "format": "date-time"}}, "additionalProperties": false, "description": "Represents a request to change the state of the light."}}}}