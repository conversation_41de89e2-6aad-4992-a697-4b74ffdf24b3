{"openapi": "3.0.1", "info": {"title": "Semantic Kernel Open API Sample", "description": "A sample Open API schema with endpoints which have security requirements defined.", "version": "1.0"}, "servers": [{"url": "https://example.org"}], "paths": {"/use_global_security": {"get": {"summary": "No security defined on operation", "description": "", "operationId": "NoSecurity", "parameters": [], "responses": {"200": {"description": "default"}}}, "post": {"summary": "Security defined on operation", "description": "", "operationId": "Security", "parameters": [], "responses": {"200": {"description": "default"}}, "security": [{"ApiKeyAuthQuery": []}]}, "put": {"summary": "Security defined on operation with new scope", "description": "", "operationId": "SecurityAndScope", "parameters": [], "responses": {"200": {"description": "default"}}, "security": [{"ApiKeyAuthQuery": []}]}}}, "components": {"securitySchemes": {"ApiKeyAuthHeader": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-KEY"}, "ApiKeyAuthQuery": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "name": "<PERSON><PERSON><PERSON><PERSON>"}}}, "security": [{"ApiKeyAuthHeader": []}]}