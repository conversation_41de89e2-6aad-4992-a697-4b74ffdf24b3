{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# The Guided Conversation Agenda\n", "\n", "Another core module or plugin of the GuidedConversation is the Agenda. This is a specialized Pydantic BaseModel that gives the agent the ability to explicitly reason about a longer term plan, or agenda, for the conversation. \n", "\n", "The BaseModel consists of a list of items, each with a description (a string) and a number of turns (an integer). It will raise an error if an input violates the type requirements. This check is particularly important for turn allocations. For example, sometimes a conversation agent provides fractional estimates (\"0.6 turns\") or broad ranges (\"5-20\" turns), both of which are meaningless. We also added additional validations which raise an error if the total number of turns allocated across items is invalid (e.g., it exceeds the number of remaining turns) depending on the resource constraint. \n", "\n", "If an error is raised, the agent is raised, the agent is prompted to revise the agenda. To prevent infinite loops, we imposed a limit on the number of retries."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Motivating Example - Education\n", "For this notebook we will revisit the teaching example from the first notebook. In that demo, under the hood the agent was actually making mistakes in its allocation of turns, mostly in generating an invalid number of cumulative turns. However, thanks to the Agenda plugin, it was able to automatically detect and correct these mistakes before they snowballed.\n", "\n", "Let's start by setting up the Agenda plugin. It takes in a resource constraint type, which as a reminder controls conversation length. Currently it can be either *maximum*  to set an upper limit and an *exact* mode for precise conversation lengths. Depending on the selected mode, the validation will differ. For example, for exact mode the total number of turns allocated across items must be exactly equal to the total number of turns available. While in maximum mode, the total number of turns allocated across items must be less than or equal to the total number of turns available."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["from semantic_kernel import Kernel\n", "from semantic_kernel.connectors.ai.open_ai import AzureChatCompletion\n", "from semantic_kernel.contents import AuthorRole, ChatMessageContent\n", "\n", "from guided_conversation.plugins.agenda import Agenda\n", "from guided_conversation.utils.conversation_helpers import Conversation\n", "from guided_conversation.utils.resources import ResourceConstraintMode\n", "\n", "RESOURCE_CONSTRAINT_TYPE = ResourceConstraintMode.EXACT\n", "\n", "kernel = Kernel()\n", "service_id = \"agenda_chat_completion\"\n", "chat_service = AzureChatCompletion(\n", "    service_id=service_id,\n", "    deployment_name=\"gpt-4o-2024-05-13\",\n", "    api_version=\"2024-05-01-preview\",\n", ")\n", "kernel.add_service(chat_service)\n", "\n", "agenda = Agenda(\n", "    kernel=kernel, service_id=service_id, resource_constraint_mode=RESOURCE_CONSTRAINT_TYPE, max_agenda_retries=2\n", ")\n", "\n", "conversation = Conversation()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here we provide an agenda that was generated by the Guided Conversation agent for the first turn of the conversation. \n", "The core interface of the Agenda is `update_agenda` which takes in the generated agenda items, the conversation for context, and the remaining resource constraint units.\n", "The expected format of the agenda is defined as follows in Pydantic:\n", "```python\n", "class _BaseAgendaItem(BaseModelLLM):\n", "    title: str = Field(description=\"Brief description of the item\")\n", "    resource: int = Field(description=\"Number of turns required for the item\")\n", "\n", "\n", "class _BaseAgenda(BaseModelLLM):\n", "    items: list[_BaseAgendaItem] = Field(\n", "        description=\"Ordered list of items to be completed in the remainder of the conversation\",\n", "        default_factory=list,\n", "    )\n", "```\n", "\n", "Since we defined the resource constraint type to be exact, the resource units must also add up exactly to the `remaining_turns` parameter.\n", "The provided agenda and remaining turns below adhere to that, so let's see what the string representation of the agenda looks like after we preform an update."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. [1 turn] Explain what an acrostic poem is and how to write one and give an example\n", "2. [2 turns] Have the student write their acrostic poem\n", "3. [2 turns] Review and give initial feedback on the student's poem\n", "4. [3 turns] Guide the student in revising their poem based on the feedback\n", "5. [3 turns] Review the revised poem and provide final feedback\n", "6. [3 turns] Address any remaining questions or details\n", "Total = 14 turns\n"]}], "source": ["generated_agenda = [\n", "    {\"title\": \"Explain what an acrostic poem is and how to write one and give an example\", \"resource\": 1},\n", "    {\"title\": \"Have the student write their acrostic poem\", \"resource\": 2},\n", "    {\"title\": \"Review and give initial feedback on the student's poem\", \"resource\": 2},\n", "    {\"title\": \"Guide the student in revising their poem based on the feedback\", \"resource\": 3},\n", "    {\"title\": \"Review the revised poem and provide final feedback\", \"resource\": 3},\n", "    {\"title\": \"Address any remaining questions or details\", \"resource\": 3},\n", "]\n", "\n", "result = await agenda.update_agenda(\n", "    items=generated_agenda,\n", "    conversation=conversation,\n", "    remaining_turns=14,\n", ")\n", "\n", "\n", "print(agenda.get_agenda_for_prompt())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, let's test out the ability of the agenda to detect and correct an agenda that does not follow the Pydantic model.\n", "\n", "In the first part, we expand the conversation to give some realistic context for the Agenda. Then, we provide an *invalid* agenda where the type of the `title` field is not a string.\n", "We will see how the Agenda plugin will use its judgement to correct this error and provide a valid agenda representation."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Was the update successful? True\n", "Agenda state: 1. [3 turns] Ask for the feedback\n", "2. [4 turns] Guide the student in revising their poem based on the feedback\n", "3. [3 turns] Review the revised poem and provide final feedback\n", "4. [2 turns] Address any remaining questions or details\n", "Total = 12 turns\n"]}], "source": ["conversation.add_messages(\n", "    ChatMessageContent(\n", "        role=AuthorRole.ASSISTANT,\n", "        content=\"\"\"Hi <PERSON>! Today, we're going to learn about acrostic poems. \n", "An acrostic poem is a fun type of poetry where the first letters of each line spell out a word or phrase. Here's how you can write one:\n", "1. Choose a word or phrase that you like. This will be the subject of your poem.\n", "2. Write the letters of your chosen word or phrase vertically down the page.\n", "3. Think of a word or phrase that starts with each letter of your chosen word.\n", "4. Write these words or phrases next to the corresponding letters to create your poem.\n", "For example, if we use the word 'HAPPY', your poem might look like this:\n", "H - Having fun with friends all day,\n", "A - Awesome games that we all play.\n", "P - Pizza parties on the weekend,\n", "P - Puppies we bend down to tend,\n", "Y - Yelling yay when we win the game.\n", "Now, why don't you try creating your own acrostic poem? Choose any word or phrase you like and follow the steps above. I can't wait to see what you come up with!\"\"\",\n", "    )\n", ")\n", "\n", "conversation.add_messages(ChatMessageContent(role=AuthorRole.USER, content=\"I want to choose cars\"))\n", "\n", "conversation.add_messages(\n", "    ChatMessageContent(\n", "        role=AuthorRole.ASSISTANT,\n", "        content=\"\"\"Great choice, <PERSON>! 'Cars' sounds like a fun subject for your acrostic poem. \n", "Be creative and let me know if you need any help as you write!\"\"\",\n", "    )\n", ")\n", "\n", "conversation.add_messages(\n", "    ChatMessageContent(\n", "        role=AuthorRole.USER,\n", "        content=\"\"\"Heres my first attempt\n", "Cruising down the street. \n", "Adventure beckons with stories untold. \\\n", "R\n", "S\"\"\",\n", "    )\n", ")\n", "\n", "result = await agenda.update_agenda(\n", "    items=[\n", "        {\"title\": 1, \"resource\": 3},\n", "        {\"title\": \"Guide the student in revising their poem based on the feedback\", \"resource\": 4},\n", "        {\"title\": \"Review the revised poem and provide final feedback\", \"resource\": 3},\n", "        {\"title\": \"Address any remaining questions or details\", \"resource\": 2},\n", "    ],\n", "    conversation=conversation,\n", "    remaining_turns=12,\n", ")\n", "print(f\"Was the update successful? {result.update_successful}\")\n", "print(f\"Agenda state: {agenda.get_agenda_for_prompt()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We see that the agent removed the invalid item and correctly reallocated the resource to other items.\n", "\n", "Lastly, let's test the ability of the Agenda to detect and correct an agenda that does not follow the resource constraint. \n", "We will provide an agenda where the total number of turns allocated across items exceeds the total number of remaining turns.\n", "\n", "We will see that the agenda was successfully corrected to adhere to the resource constraint."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Was the update successful? True\n", "Agenda state: 1. [7 turns] Review the revised poem and provide final feedback\n", "2. [4 turns] Address any remaining questions or details\n", "Total = 11 turns\n"]}], "source": ["conversation.add_messages(\n", "    ChatMessageContent(\n", "        role=AuthorRole.ASSISTANT,\n", "        content=\"\"\"That's a great start, <PERSON>! I love the imagery you've used in your poem. Let's continue with writing the \"R\" and \"S\" lines.\"\"\",\n", "    )\n", ")\n", "\n", "conversation.add_messages(\n", "    ChatMessageContent(\n", "        role=AuthorRole.USER,\n", "        content=\"\"\"Sure here's the rest of the poem:\n", "Cruising down the street. \n", "Adventure beckons with stories untold.\n", "Revving engines, vroom vroom. \n", "Steering through life's twists and turns.\"\"\",\n", "    )\n", ")\n", "\n", "result = await agenda.update_agenda(\n", "    items=[\n", "        {\"title\": \"Review the revised poem and provide final feedback\", \"resource\": 4},\n", "        {\"title\": \"Address any remaining questions or details\", \"resource\": 3},\n", "    ],\n", "    conversation=conversation,\n", "    remaining_turns=11,\n", ")\n", "\n", "print(f\"Was the update successful? {result.update_successful}\")\n", "print(f\"Agenda state: {agenda.get_agenda_for_prompt()}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}