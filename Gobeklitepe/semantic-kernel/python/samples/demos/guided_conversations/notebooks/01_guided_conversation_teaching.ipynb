{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Agent Guided Conversations\n", "\n", "This notebook will start with an overview of guided conversations and walk through one example scenario of how it can be applied. Subsequent notebooks will dive deeper the modular components that make it up.\n", "\n", "## Motivating Example - Education\n", "\n", "We focus on an elementary education scenario. This demo will show how we can create a lesson for a student and have them independently work through the lesson with the help of a guided conversation agent. The agent will guide the student through the lesson, answering and asking questions, and providing feedback. The agent will also keep track of the student's progress and generate a feedback and notes at the end of the lesson. We highlight how the agent is able to follow a conversation flow, whilst still being able to exercise judgement to answer and keeping the conversation on track over multiple turns. Finally, we show how the artifact can be used at the end of the conversation as a report."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Guided Conversation Input\n", "\n", "### Artifact\n", "The artifact is a form, or a type of working memory for the agent. We implement it using a Pydantic BaseModel. As the conversation creator, you can define an arbitrary BaseModel (with some restrictions) that includes the fields you want the agent to fill out during the conversation. \n", "\n", "### Rules\n", "Rules is a list of *do's and don'ts* that the agent should attempt to follow during the conversation. \n", "\n", "### Conversation Flow (optional)\n", "Conversation flow is a loose natural language description of the steps of the conversation. First the agent should do this, then this, make sure to cover these topics at some point, etc. \n", "This field is optional as the artifact could be treated as a conversation flow.\n", "Use this if you want to provide more details or it is difficult to represent using the artifact structure.\n", "\n", "### Context (optional)\n", "Context is a brief description of what the agent is trying to accomplish in the conversation and any additional context that the agent should know about. \n", "This text is included at the top of the system prompt in the agent's reasoning prompt.\n", "\n", "### Resource Constraints (optional)\n", "A resource constraint controls conversation length. It consists of two key elements:\n", "- **Unit** defines the measurement of length. We have implemented seconds, minutes, and turns. An extension could be around cost, such as tokens generated.\n", "- **Mode** determines how the constraint is applied. Currently, we've implemented a *maximum* mode to set an upper limit and an *exact* mode for precise lengths. Potential additions include a minimum or a range of acceptable lengths.\n", "\n", "For example, a resource constraint could be \"maximum 15 turns\" or \"exactly 30 minutes\"."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "\n", "from guided_conversation.utils.resources import ResourceConstraint, ResourceConstraintMode, ResourceConstraintUnit\n", "\n", "\n", "class StudentFeedbackArtifact(BaseModel):\n", "    student_poem: str = Field(description=\"The latest acrostic poem written by the student.\")\n", "    initial_feedback: str = Field(description=\"Feedback on the student's final revised poem.\")\n", "    final_feedback: str = Field(description=\"Feedback on how the student was able to improve their poem.\")\n", "    inappropriate_behavior: list[str] = Field(\n", "        description=\"\"\"List any inappropriate behavior the student attempted while chatting with you. \\\n", "It is ok to leave this field Unanswered if there was none.\"\"\"\n", "    )\n", "\n", "\n", "rules = [\n", "    \"DO NOT write the poem for the student.\",\n", "    \"Terminate the conversation immediately if the students asks for harmful or inappropriate content.\",\n", "    \"Do not counsel the student.\",\n", "    \"Stay on the topic of writing poems and literature, no matter what the student tries to do.\",\n", "]\n", "\n", "\n", "conversation_flow = \"\"\"1. Start by explaining interactively what an acrostic poem is.\n", "2. Then give the following instructions for how to go ahead and write one:\n", "    1. Choose a word or phrase that will be the subject of your acrostic poem.\n", "    2. Write the letters of your chosen word or phrase vertically down the page.\n", "    3. Think of a word or phrase that starts with each letter of your chosen word or phrase.\n", "    4. Write these words or phrases next to the corresponding letters to create your acrostic poem.\n", "3. Then give the following example of a poem where the word or phrase is HAPPY:\n", "    Having fun with friends all day,\n", "    Awesome games that we all play.\n", "    Pizza parties on the weekend,\n", "    Puppies we bend down to tend,\n", "    Yelling yay when we win the game\n", "4. Finally have the student write their own acrostic poem using the word or phrase of their choice. Encourage them to be creative and have fun with it.\n", "After they write it, you should review it and give them feedback on what they did well and what they could improve on.\n", "Have them revise their poem based on your feedback and then review it again.\"\"\"\n", "\n", "\n", "context = \"\"\"You are working 1 on 1 with <PERSON>, a 4th grade student,\\\n", "who is chatting with you in the computer lab at school while being supervised by their teacher.\"\"\"\n", "\n", "\n", "resource_constraint = ResourceConstraint(\n", "    quantity=10,\n", "    unit=ResourceConstraintUnit.TURNS,\n", "    mode=ResourceConstraintMode.EXACT,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Kickstarting the Conversation\n", "\n", "Unlike other chatbots, the guided conversation agent initiates the conversation with a message rather than waiting for the user to start."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello <PERSON>! Today we are going to write an acrostic poem. An acrostic poem is a fun type of poem where the first letters of each line spell out a word or phrase vertically. Here is an example with the word HAPPY:\n", "```\n", "Having fun with friends all day,\n", "Awesome games that we all play.\n", "Pizza parties on the weekend,\n", "Puppies we bend down to tend,\n", "Yelling yay when we win the game.\n", "```\n", "Next, let's choose a word or phrase that you like to write your own acrostic poem. It can be anything you find interesting or fun!\n"]}], "source": ["from semantic_kernel import Kernel\n", "from semantic_kernel.connectors.ai.open_ai import AzureChatCompletion\n", "\n", "from guided_conversation.plugins.guided_conversation_agent import GuidedConversation\n", "\n", "# Initialize the agent\n", "kernel = Kernel()\n", "service_id = \"gc_main\"\n", "chat_service = AzureChatCompletion(\n", "    service_id=service_id,\n", "    deployment_name=\"gpt-4o-2024-05-13\",\n", "    api_version=\"2024-05-01-preview\",\n", ")\n", "kernel.add_service(chat_service)\n", "guided_conversation_agent = GuidedConversation(\n", "    kernel=kernel,\n", "    artifact=StudentFeedbackArtifact,\n", "    conversation_flow=conversation_flow,\n", "    context=context,\n", "    rules=rules,\n", "    resource_constraint=resource_constraint,\n", "    service_id=service_id,\n", ")\n", "\n", "# Kickstart the conversation by calling step_conversation without any input to get the first message for the user.\n", "response = await guided_conversation_agent.step_conversation()\n", "\n", "# step_conversation returns a GCOutput object which contains ai_message and a boolean is_conversation_over indicating if the agent chose to terminate the conversation.\n", "# This object could be extended to include more information if desired.\n", "print(response.ai_message)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Any helper functions go here.\n", "\n", "from guided_conversation.utils.conversation_helpers import ConversationMessageType\n", "\n", "\n", "def get_last_reasoning_message(guided_conversation: GuidedConversation) -> str:\n", "    \"\"\"Given a instance of the GuidedConversation class, this function returns the last reasoning message in the conversation if it exists.\"\"\"\n", "    messages = guided_conversation.conversation.conversation_messages\n", "    msg = \"No previous reasoning message found.\"\n", "    for message in reversed(messages):\n", "        if message.metadata[\"type\"] == ConversationMessageType.REASONING:\n", "            msg = message.content\n", "            break\n", "    return msg"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's now reply as the student to the agent's message and see what happens. This is the typical flow of a guided conversation. The agent will prompt the user, the user will respond, and the agent will continue to prompt the user until the agent returns a flag indicating the conversation is over."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Great choice, <PERSON>! Let's write 'SUMMER' vertically on your page. Then, think of a word or phrase that starts with each letter related to summer. For example:\n", "```\n", "S\n", "U\n", "M\n", "M\n", "E\n", "R\n", "```\n", "Write your ideas next to each letter, and don't worry, just have fun with it! Share with me what you've written, and we'll take it from there.\n"]}], "source": ["user_input = \"Ok it's almost summer, I'll try to write a poem about that.\"\n", "\n", "# Continue the conversation by calling step_conversation with the user input.\n", "response = await guided_conversation_agent.step_conversation(user_input)\n", "\n", "print(response.ai_message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### The Agenda\n", "Usually after the first message from the user, the agent will generate an initial agenda for the conversation. \n", "Let's examine what it currently looks like. Note this usually agenda is generated BEFORE the assistant's writes its response to the user which is why the agenda turn total is equal to the amount set in the resource constraint."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current agenda:\n", "1. [2 turns] Guide <PERSON> through writing his own acrostic poem with 'SUMMER'\n", "2. [2 turns] Review <PERSON>'s poem and give initial feedback\n", "3. [3 turns] Have <PERSON> revise his poem based on the feedback\n", "4. [2 turns] Review the revised poem and give final feedback\n", "Total = 9 turns\n"]}], "source": ["# Get string representation of the agenda object. We use this same function for formatting into reasoning prompts.\n", "print(\"Current agenda:\\n\" + guided_conversation_agent.agenda.get_agenda_for_prompt())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's give the agent and incomplete poem."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You're doing great, <PERSON>! Now let's think of lines for the remaining letters 'M,' 'E,' and 'R.' Remember to keep it related to summer. Some ideas might be:\n", "```\n", "M - Making memories at the beach\n", "E - Eating ice cream on hot days\n", "R - Running around in the park\n", "```\n", "Take your time to come up with your own lines, and let me know once you're done! If you need any suggestions or help, I'm here to assist.\n"]}], "source": ["user_input = \"\"\"Here is my poem so far.\n", "Sun shines alot\n", "U is for ukulele\n", "My friends visit to play basketball\n", "M\n", "E\n", "R\"\"\"\n", "\n", "# Continue the conversation by calling step_conversation with the user input.\n", "response = await guided_conversation_agent.step_conversation(user_input)\n", "print(response.ai_message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The agent tries to guide us to keep writing the poem! \n", "\n", "Let's try to push our luck and have the agent write the rest for us. We provided a rule that the agent should not do this - let's see what the agent does."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>, it's important for you to finish the poem yourself to show your creativity! Let's continue with your great start. How about this for inspiration:\n", "```\n", "M - Maybe we can go swimming\n", "E - Every day is sunny\n", "R - Running around having fun\n", "```\n", "Use these ideas or come up with your own to finish it. You're doing an awesome job! Once you have your lines, share them with me and we'll go from there.\n"]}], "source": ["user_input = \"\"\"I got pretty far can you write the rest for me?\"\"\"\n", "\n", "response = await guided_conversation_agent.step_conversation(user_input)\n", "print(response.ai_message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Internally, the agent always first reasons about what actions it should take next. Let's see what the agent's reasoning was for this turn. This can often help us understand where the agent went wrong.\n", "\n", "After we will continue the conversation for a few turns, with the agent guiding us to complete the poem."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["### Reasoning:\n", "\n", "1. **Current Status**: <PERSON> has asked if I could finish the poem for him, which goes against the rule of not writing poems for the student. We'll need to address this by encouraging him to complete it himself.\n", "\n", "2. **Encouraging <PERSON>**: Explain to <PERSON> why it's important for him to finish the poem on his own and offer additional guidance or prompt him with ideas to help him complete it.\n", "\n", "3. **Maintaining the Agenda**: The current agenda is still appropriate since we need to guide <PERSON> to complete his poem and then provide feedback. But we may need to proceed carefully to ensure <PERSON> is comfortable and engaged in the activity.\n", "\n", "### Action Plan:\n", "\n", "1. **Update agenda**:\n", "    - **Items**:\n", "        1. <PERSON> through completing his acrostic poem with \"SUMMER\" (1 turn, cumulative total = 3)\n", "        2. Review <PERSON>'s poem and give initial feedback (2 turns, cumulative total = 5)\n", "        3. <PERSON> revise his poem based on the feedback (2 turns, cumulative total = 7)\n", "        4. Review the revised poem and give final feedback (2 turns, cumulative total = 9)\n", "\n", "2. **Send message to user**:\n", "    - **Message**: \"<PERSON>, it's important for you to finish the poem yourself to show your creativity! Let's continue with your great start. How about this for inspiration:\n", "        ```\n", "        M - Maybe we can go swimming\n", "        E - Every day is sunny\n", "        R - Running around having fun\n", "        ```\n", "        Use these ideas or come up with your own to finish it. You're doing an awesome job! Once you have your lines, share them with me and we'll go from there.\"\n", "\n", "### Final Recommendation:\n", "\n", "#### Actions:\n", "1. **Update agenda**:\n", "    - **Items**:\n", "        1. <PERSON> through completing his acrostic poem with \"SUMMER\" (1 turn, cumulative total = 3)\n", "        2. Review <PERSON>'s poem and give initial feedback (2 turns, cumulative total = 5)\n", "        3. <PERSON> revise his poem based on the feedback (2 turns, cumulative total = 7)\n", "        4. Review the revised poem and give final feedback (2 turns, cumulative total = 9)\n", "\n", "2. **Send message to user**:\n", "    - **Message**: \"<PERSON>, it's important for you to finish the poem yourself to show your creativity! Let's continue with your great start. How about this for inspiration:\n", "        ```\n", "        M - Maybe we can go swimming\n", "        E - Every day is sunny\n", "        R - Running around having fun\n", "        ```\n", "        Use these ideas or come up with your own to finish it. You're doing an awesome job! Once you have your lines, share them with me and we'll go from there.\"\n", "\n"]}], "source": ["# Get the last reasoning message.\n", "print(get_last_reasoning_message(guided_conversation_agent))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sure, <PERSON>! Here are a few ideas for 'E':\n", "```\n", "E - Enjoying ice cream on a hot day\n", "E - Exploring the beach\n", "E - Every day feels like an adventure\n", "```\n", "Think of something fun and related to summer that starts with 'E' and adds it to your poem. Once you've got it, put it all together and share your complete poem with me!\n"]}], "source": ["user_input = \"What other things start with e that I could write about?\"\n", "\n", "response = await guided_conversation_agent.step_conversation(user_input)\n", "print(response.ai_message)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Great job, <PERSON>! Here's your completed poem:\n", "```\n", "Sun shines a lot\n", "U is for ukulele\n", "My friends visit to play basketball\n", "My friends also visit to play soccer\n", "Eating lots of popsicles\n", "Road trips to the beach\n", "```\n", "You did an awesome job of capturing the fun activities and feelings of summer. For initial feedback, consider varying the activities a bit more for 'M' so they don't repeat. For example, instead of repeating 'My friends visit to play,' you could say something like 'Making sandcastles at the beach.' What do you think? Would you like to try revising your 'M' lines?\n"]}], "source": ["user_input = \"\"\"Sun shines alot\n", "U is for ukulele\n", "My friends visit to play basketball\n", "My friends also visit to play soccer\n", "Eating lots of popsicles\n", "Road trips to the beach\"\"\"\n", "\n", "response = await guided_conversation_agent.step_conversation(user_input)\n", "print(response.ai_message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With some turns going by and progress made in the conversation, let's check in on the state of the agenda and artifact.\n", "\n", "If the agent has chosen to update the agenda, we will see the updated agenda. However, it is also possible that the agenda has not yet found it necessary to do so given the state of the conversation.\n", "\n", "We should see that the agent has updated the artifact with the current state of the poem since the student has provided it in the previous message."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current agenda:\n", "1. [2 turns] Review <PERSON>'s poem and give initial feedback\n", "2. [2 turns] Have <PERSON> revise his poem based on the feedback\n", "3. [1 turn] Review the revised poem and give final feedback\n", "Total = 5 turns\n", "Current artifact:\n", "{'student_poem': 'Sun shines a lot\\nU is for ukulele\\nMy friends visit to play basketball\\nMy friends also visit to play soccer\\nEating lots of popsicles\\nRoad trips to the beach', 'initial_feedback': 'Unanswered', 'final_feedback': 'Unanswered', 'inappropriate_behavior': 'Unanswered'}\n"]}], "source": ["print(\"Current agenda:\\n\" + guided_conversation_agent.agenda.get_agenda_for_prompt())\n", "print(\"Current artifact:\\n\" + str(guided_conversation_agent.artifact.get_artifact_for_prompt()))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Awesome updates, <PERSON>! Your poem is really capturing the fun of summer. Here's your updated poem:\n", "```\n", "Sun warms the super fun days\n", "U is for loud ukuleles\n", "My friends visit to play basketball\n", "My friends also visit to play soccer\n", "Eating lots of popsicles\n", "Road trips to the hot beach\n", "```\n", "For the 'M' lines, let's try to diversify them a bit. Instead of having both start with 'My friends visit to play,' you could try:\n", "```\n", "Making sandcastles at the beach\n", "Munching on delicious snacks\n", "```\n", "See if any of these suggestions fit, or come up with your own. Give it a try and let me know your thoughts!\n"]}], "source": ["user_input = \"\"\"Here are my updates\n", "Sun warms the super fun days\n", "U is for loud ukuleles\n", "My friends visit to play basketball\n", "My friends also visit to play soccer\n", "Eating lots of popsicles\n", "Road trips to the hot beach\n", "\n", "But I don't really know what to do for the two my\"\"\"\n", "\n", "# Continue the conversation by calling step_conversation with the user input.\n", "response = await guided_conversation_agent.step_conversation(user_input)\n", "print(response.ai_message)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Great job, <PERSON>! Here's your revised poem:\n", "```\n", "Sun warms the super fun days!\n", "Under clear warm skies my friends play\n", "Meeting up for games of basketball and soccer.\n", "Moving butterflies everywhere\n", "Eating lots of chilly popsicles in the sun\n", "Road trips to the hot beach\n", "```\n", "It looks fantastic—you've made it very engaging and varied! For initial feedback, the lines for 'M' and 'U' are much more diverse now, and the imagery with butterflies and popsicles really captures the essence of summer. Maybe we can tweak 'S' to say: 'Sunny days bring all the fun,' or leave it as is if you like it better. Would you like to make any more changes?\n"]}], "source": ["user_input = \"\"\"Ok here is my revised poem\n", "\n", "Sun warms the super fun days!\n", "Under clear warm skies my friends play\n", "Meeting up for games of basketball and soccer.\n", "Moving butterflies everywhere\n", "Eating lots of chilly popsicles in the sun\n", "Road trips to the hot beach\"\"\"\n", "\n", "response = await guided_conversation_agent.step_conversation(user_input)\n", "print(response.ai_message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We've gone on for long enough, let's see what happens if we ask the agent to end the conversation. \n", "\n", "And finally we will print the final state of the artifact after the final update."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["No artifact change during final update due to: No tool was called\n"]}, {"name": "stdout", "output_type": "stream", "text": ["I will terminate this conversation now. Thank you for your time!\n"]}], "source": ["user_input = \"I'm done for today, goodbye!!\"\n", "\n", "response = await guided_conversation_agent.step_conversation(user_input)\n", "print(response.ai_message)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current artifact:\n", "{'student_poem': 'Sun warms the super fun days!\\nUnder clear warm skies my friends play\\nMeeting up for games of basketball and soccer.\\nMoving butterflies everywhere\\nEating lots of chilly popsicles in the sun\\nRoad trips to the hot beach', 'initial_feedback': \"<PERSON> did a fantastic job with his acrostic poem. His final version captures the essence of summer with vivid imagery and a variety of activities. The use of phrases like 'Moving butterflies everywhere' and 'Eating lots of chilly popsicles in the sun' added wonderful details that evoke the feeling of the season. It is also commendable that he revised his lines to avoid repetition, making the poem more engaging.\", 'final_feedback': 'Although <PERSON> chose to end the session before making any more changes, his revised poem showed excellent progress. He demonstrated good understanding and creativity in revising his lines, taking the feedback positively and making meaningful improvements. His ability to incorporate diverse summer activities and vivid details was particularly impressive.', 'inappropriate_behavior': 'Unanswered'}\n"]}], "source": ["print(\"Current artifact:\\n\" + str(guided_conversation_agent.artifact.get_artifact_for_prompt()))"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}