{"flow_runs": [{"run_id": "fae59adc-46fb-4ac3-bb72-ccdbaba38eaf_0", "status": "Completed", "error": null, "inputs": {"deployment_name": "gpt-35-turbo", "deployment_type": "chat-completion", "text": "What is 5+3"}, "output": {"result": "8.0"}, "metrics": null, "request": null, "parent_run_id": "fae59adc-46fb-4ac3-bb72-ccdbaba38eaf", "root_run_id": "fae59adc-46fb-4ac3-bb72-ccdbaba38eaf", "source_run_id": null, "flow_id": "template_standard_flow", "start_time": "2023-09-15T14:46:16.174635Z", "end_time": "2023-09-15T14:46:17.804698Z", "index": 0, "api_calls": [{"name": "my_python_tool", "type": "Tool", "inputs": {"AzureOpenAIConnection": "AzureOpenAIConnection", "deployment_name": "gpt-35-turbo", "deployment_type": "chat-completion", "input": "What is 5+3"}, "output": "8.0", "start_time": 1694785576.175247, "end_time": 1694785577.803631, "error": null, "children": null, "node_name": "math_planner"}], "variant_id": "", "name": "", "description": "", "tags": null, "system_metrics": {"duration": 1.630063, "total_tokens": 0}, "result": {"result": "8.0"}, "upload_metrics": false}], "node_runs": [{"node": "math_planner", "flow_run_id": "fae59adc-46fb-4ac3-bb72-ccdbaba38eaf", "run_id": "fae59adc-46fb-4ac3-bb72-ccdbaba38eaf_math_planner_0", "status": "Completed", "inputs": {"AzureOpenAIConnection": "AzureOpenAIConnection", "deployment_name": "gpt-35-turbo", "deployment_type": "chat-completion", "input": "What is 5+3"}, "output": "8.0", "metrics": null, "error": null, "parent_run_id": "fae59adc-46fb-4ac3-bb72-ccdbaba38eaf_0", "start_time": "2023-09-15T14:46:16.175198Z", "end_time": "2023-09-15T14:46:17.803940Z", "index": 0, "api_calls": [{"name": "my_python_tool", "type": "Tool", "inputs": {"AzureOpenAIConnection": "AzureOpenAIConnection", "deployment_name": "gpt-35-turbo", "deployment_type": "chat-completion", "input": "What is 5+3"}, "output": "8.0", "start_time": 1694785576.175247, "end_time": 1694785577.803631, "error": null, "children": null, "node_name": "math_planner"}], "variant_id": "", "cached_run_id": null, "cached_flow_run_id": null, "logs": {"stdout": "[2023-09-15T14:46:17+0000] Function: MathPlugin.Add\n[2023-09-15T14:46:17+0000] Input vars: {'input': '5', 'number2': '3'}\n[2023-09-15T14:46:17+0000] Output vars: ['RESULT__STEP_1']\n[2023-09-15T14:46:17+0000] Result: 8.0\n", "stderr": ""}, "system_metrics": {"duration": 1.628742}, "result": "8.0"}]}