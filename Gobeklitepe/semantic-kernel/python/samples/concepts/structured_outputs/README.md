# OpenAI Structured Outputs

## Supported Models

### Azure OpenAI:

- Access to `gpt-4o-2024-08-06` or later
- The `2024-08-01-preview` API version
- If using a token instead of an API key, you must have the `Cognitive Services OpenAI Contributor` role assigned to your Azure AD user.
- See more information [here](https://learn.microsoft.com/en-us/azure/ai-services/openai/how-to/structured-outputs?tabs=python-secure)

### OpenAI:

- The OpenAI models supported are:
  - `gpt-4o-mini-2024-07-18` and later
  - `gpt-4o-2024-08-06` and later
