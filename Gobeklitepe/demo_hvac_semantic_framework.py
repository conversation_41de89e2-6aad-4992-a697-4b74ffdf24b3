#!/usr/bin/env python3
"""
🚀 HVAC Semantic Framework Demo
Demonstration of the most powerful semantic analysis framework for HVAC operations
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Import framework components
try:
    from hvac_semantic_framework.core.hvac_semantic_framework import (
        HVACSemanticFramework, 
        FrameworkConfig
    )
    FRAMEWORK_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Framework import failed: {e}")
    print("📦 Please install requirements: pip install -r requirements.txt")
    FRAMEWORK_AVAILABLE = False

class HVACSemanticDemo:
    """
    🎯 Comprehensive demonstration of HVAC Semantic Framework capabilities
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.framework: HVACSemanticFramework = None
        
    async def run_demo(self):
        """
        🚀 Run complete framework demonstration
        """
        print("🚀 HVAC Semantic Framework Demo Starting...")
        print("=" * 60)
        
        if not FRAMEWORK_AVAILABLE:
            print("❌ Framework not available. Please check installation.")
            return
        
        try:
            # Initialize framework
            await self._initialize_framework()
            
            # Demo 1: Customer Communication Analysis
            await self._demo_communication_analysis()
            
            # Demo 2: HVAC Knowledge Search
            await self._demo_knowledge_search()
            
            # Demo 3: Equipment Data Processing
            await self._demo_equipment_processing()
            
            # Demo 4: Service Workflow Creation
            await self._demo_service_workflow()
            
            # Demo 5: Framework Status and Metrics
            await self._demo_framework_status()
            
            print("\n🎉 Demo completed successfully!")
            
        except Exception as e:
            self.logger.error(f"❌ Demo failed: {e}")
            print(f"❌ Demo failed: {e}")
        
        finally:
            if self.framework:
                await self.framework.shutdown()
    
    async def _initialize_framework(self):
        """Initialize the HVAC Semantic Framework"""
        print("\n🔧 Initializing HVAC Semantic Framework...")
        
        # Create configuration
        config = FrameworkConfig(
            weaviate_url="http://localhost:8080",
            enable_real_time=True,
            enable_monitoring=True,
            max_concurrent_agents=5
        )
        
        # Initialize framework
        self.framework = HVACSemanticFramework(config)
        
        # Note: In real deployment, Weaviate should be running
        # For demo, we'll simulate initialization
        print("⚠️ Note: This demo requires Weaviate running on localhost:8080")
        print("📦 To start Weaviate: docker run -d -p 8080:8080 semitechnologies/weaviate:latest")
        
        # Simulate successful initialization for demo
        print("✅ Framework initialized (simulated)")
    
    async def _demo_communication_analysis(self):
        """Demo customer communication analysis"""
        print("\n📧 Demo 1: Customer Communication Analysis")
        print("-" * 40)
        
        # Sample customer communications
        communications = [
            {
                "text": "My LG air conditioner is not cooling properly. It's making strange noises and the air coming out is warm. This is urgent as it's very hot today!",
                "customer_id": "CUST001",
                "type": "email"
            },
            {
                "text": "I need to schedule maintenance for my Daikin heat pump. It's been working fine but I want to keep it in good condition.",
                "customer_id": "CUST002", 
                "type": "phone"
            },
            {
                "text": "Emergency! Our office HVAC system has completely stopped working. No air flow at all. We need immediate service!",
                "customer_id": "CUST003",
                "type": "emergency"
            }
        ]
        
        for i, comm in enumerate(communications, 1):
            print(f"\n📝 Communication {i}:")
            print(f"   Text: {comm['text'][:80]}...")
            print(f"   Customer: {comm['customer_id']}")
            print(f"   Type: {comm['type']}")
            
            # Simulate analysis result
            result = {
                "success": True,
                "semantic_analysis": {
                    "sentiment": {"label": "NEGATIVE" if "emergency" in comm['text'].lower() else "NEUTRAL", "score": 0.8},
                    "intent": {"label": "service_request", "confidence": 0.9},
                    "urgency_score": 0.9 if "emergency" in comm['text'].lower() else 0.3,
                    "hvac_concepts": ["lg air conditioner", "cooling issues"] if "LG" in comm['text'] else ["maintenance"],
                    "keywords": ["cooling", "noise", "urgent"] if "noise" in comm['text'] else ["maintenance", "schedule"]
                },
                "processing_time": 0.15
            }
            
            print(f"   ✅ Analysis: {result['semantic_analysis']['sentiment']['label']} sentiment")
            print(f"   🎯 Intent: {result['semantic_analysis']['intent']['label']}")
            print(f"   ⚡ Urgency: {result['semantic_analysis']['urgency_score']:.1f}")
            print(f"   🔧 HVAC Concepts: {', '.join(result['semantic_analysis']['hvac_concepts'])}")
    
    async def _demo_knowledge_search(self):
        """Demo HVAC knowledge search"""
        print("\n🔍 Demo 2: HVAC Knowledge Search")
        print("-" * 40)
        
        search_queries = [
            "LG air conditioner troubleshooting",
            "Daikin heat pump maintenance schedule", 
            "HVAC system not cooling properly",
            "Emergency HVAC repair procedures"
        ]
        
        for query in search_queries:
            print(f"\n🔍 Searching: '{query}'")
            
            # Simulate search results
            results = [
                {
                    "title": f"HVAC Guide: {query}",
                    "content": f"Comprehensive guide for {query.lower()}...",
                    "category": "troubleshooting",
                    "confidence": 0.95
                },
                {
                    "title": f"Technical Manual: {query}",
                    "content": f"Technical documentation for {query.lower()}...",
                    "category": "documentation", 
                    "confidence": 0.87
                }
            ]
            
            print(f"   ✅ Found {len(results)} results")
            for i, result in enumerate(results, 1):
                print(f"   {i}. {result['title']} (confidence: {result['confidence']:.2f})")
    
    async def _demo_equipment_processing(self):
        """Demo equipment data processing"""
        print("\n🔧 Demo 3: Equipment Data Processing")
        print("-" * 40)
        
        equipment_data = [
            {
                "brand": "LG",
                "model": "LS120HSV5",
                "type": "Split System Air Conditioner",
                "capacity": 12000,
                "energyRating": "SEER 22",
                "specifications": "Dual Inverter Compressor, Wi-Fi enabled, 10-year warranty"
            },
            {
                "brand": "Daikin", 
                "model": "DX16SA",
                "type": "Heat Pump",
                "capacity": 24000,
                "energyRating": "SEER 16",
                "specifications": "Variable speed compressor, R-410A refrigerant, outdoor unit"
            }
        ]
        
        for equipment in equipment_data:
            print(f"\n🔧 Processing: {equipment['brand']} {equipment['model']}")
            print(f"   Type: {equipment['type']}")
            print(f"   Capacity: {equipment['capacity']} BTU")
            print(f"   Rating: {equipment['energyRating']}")
            
            # Simulate processing result
            result = {
                "success": True,
                "enhanced_data": {
                    **equipment,
                    "semanticTags": ["hvac", "air conditioning", equipment['brand'].lower(), "energy efficient"],
                    "lastUpdated": datetime.now().isoformat()
                },
                "processing_time": 0.08
            }
            
            print(f"   ✅ Enhanced with semantic tags: {', '.join(result['enhanced_data']['semanticTags'])}")
    
    async def _demo_service_workflow(self):
        """Demo service workflow creation"""
        print("\n🔄 Demo 4: Service Workflow Creation")
        print("-" * 40)
        
        service_requests = [
            {
                "description": "Customer reports LG air conditioner not cooling, making loud noises",
                "type": "repair",
                "priority": "high",
                "customer_id": "CUST001"
            },
            {
                "description": "Scheduled maintenance for Daikin heat pump system",
                "type": "maintenance", 
                "priority": "medium",
                "customer_id": "CUST002"
            }
        ]
        
        for request in service_requests:
            print(f"\n🔄 Creating workflow for: {request['description'][:50]}...")
            print(f"   Type: {request['type']}")
            print(f"   Priority: {request['priority']}")
            
            # Simulate workflow creation
            workflow_result = {
                "success": True,
                "workflow_id": f"WF_{datetime.now().timestamp():.0f}",
                "task_ids": ["TASK_001", "TASK_002", "TASK_003"],
                "estimated_completion": "2-4 hours"
            }
            
            print(f"   ✅ Workflow created: {workflow_result['workflow_id']}")
            print(f"   📋 Tasks: {len(workflow_result['task_ids'])} agents assigned")
            print(f"   ⏱️ Estimated completion: {workflow_result['estimated_completion']}")
    
    async def _demo_framework_status(self):
        """Demo framework status and metrics"""
        print("\n📊 Demo 5: Framework Status and Metrics")
        print("-" * 40)
        
        # Simulate framework status
        status = {
            "initialized": True,
            "uptime_seconds": 300,
            "components": {
                "weaviate": {"status": "healthy", "last_check": datetime.now().isoformat()},
                "semantic_analyzer": {"status": "initialized"},
                "agent_orchestrator": {
                    "status": "operational",
                    "active_tasks": 2,
                    "completed_tasks": 8,
                    "success_rate": 0.95
                }
            },
            "metrics": {
                "total_analyses": 15,
                "total_searches": 8,
                "total_agent_tasks": 10,
                "average_response_time": 0.12,
                "success_rate": 0.96
            }
        }
        
        print(f"🟢 Framework Status: {'Healthy' if status['initialized'] else 'Unhealthy'}")
        print(f"⏱️ Uptime: {status['uptime_seconds']} seconds")
        print(f"📊 Total Analyses: {status['metrics']['total_analyses']}")
        print(f"🔍 Total Searches: {status['metrics']['total_searches']}")
        print(f"🤖 Agent Tasks: {status['metrics']['total_agent_tasks']}")
        print(f"⚡ Avg Response Time: {status['metrics']['average_response_time']:.3f}s")
        print(f"✅ Success Rate: {status['metrics']['success_rate']:.1%}")
        
        print("\n🔧 Component Status:")
        for component, info in status['components'].items():
            print(f"   {component}: {info['status']}")

async def main():
    """Main demo function"""
    demo = HVACSemanticDemo()
    await demo.run_demo()

if __name__ == "__main__":
    print("🚀 HVAC Semantic Framework - Advanced Demo")
    print("🔥 Most Powerful Semantic Analysis for HVAC Operations")
    print("=" * 60)
    
    # Run the demo
    asyncio.run(main())