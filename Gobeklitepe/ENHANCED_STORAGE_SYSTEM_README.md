# 🗄️ Enhanced Storage System for HVAC CRM

## 🎯 Przegląd

Zaawansowany system zarządzania plikami M4A i dokumentów z wykorzystaniem MinIO jako centralnego storage'u dla systemu HVAC CRM. System automatyzuje pobieranie, dekodowanie, przetwarzanie i przygotowywanie plików audio do transkrypcji.

## ✨ Główne funkcjonalności

### 📦 **Enhanced MinIO Manager**
- ✅ Automatyczne zarządzanie bucketami MinIO
- ✅ Upload plików M4A z metadanymi
- ✅ Dekodowanie załączników base64
- ✅ Wyciąganie informacji o klientach z nazw plików
- ✅ Obliczanie hash MD5 i deduplikacja
- ✅ Statystyki storage'u

### 🔄 **File Processing Pipeline**
- ✅ Automatyczne przetwarzanie załączników Dolores
- ✅ Sprawdzanie jakości plików audio
- ✅ Przygotowywanie do transkrypcji
- ✅ Generowanie szczegółowych raportów
- ✅ Zarządzanie kolejką transkrypcji

### 🎛️ **Gradio Storage Interface**
- ✅ Interfejs webowy do zarządzania storage'em
- ✅ Statystyki w czasie rzeczywistym
- ✅ Monitoring kolejki transkrypcji
- ✅ Automatyczne przetwarzanie plików
- ✅ Zarządzanie plikami tymczasowymi

## 🏗️ Architektura

```
📧 Email Attachments (Base64)
    ↓
🔄 File Processing Pipeline
    ↓
🗄️ MinIO Storage (Buckety)
    ↓
📋 Transcription Queue
    ↓
🎤 NVIDIA NeMo STT
```

### 🗂️ **Struktura Bucketów MinIO**

| Bucket | Przeznaczenie | Przykład |
|--------|---------------|----------|
| `hvac-audio-files` | Pliki audio M4A | `m4a/20241231_abc123_recording.m4a` |
| `hvac-documents` | Dokumenty i metadata | `metadata/abc123.json` |
| `hvac-transcriptions` | Wyniki transkrypcji | `transcriptions/abc123_result.json` |
| `hvac-processed-files` | Przetworzone pliki | `processed/abc123_enhanced.m4a` |
| `hvac-temp-files` | Pliki tymczasowe (7 dni) | `temp/processing_abc123.tmp` |

## 🚀 Instalacja i uruchomienie

### 1. **Instalacja zależności**
```bash
cd python_mixer
pip install -r requirements_storage.txt
```

### 2. **Konfiguracja MinIO**
Sprawdź konfigurację w `config.yaml`:
```yaml
minio:
  endpoint: "**************:9000"
  access_key_id: "koldbringer"
  secret_access_key: "Blaeritipol1"
  use_ssl: false
```

### 3. **Uruchomienie systemu**

#### **Pełny system (z interfejsem)**
```bash
python launch_enhanced_storage_system.py
```

#### **Tylko interfejs Gradio**
```bash
python launch_enhanced_storage_system.py --interface-only
```

#### **Sprawdzenie stanu systemu**
```bash
python launch_enhanced_storage_system.py --health-check
```

#### **Przetwarzanie załączników**
```bash
python launch_enhanced_storage_system.py --process-attachments
```

## 🎛️ Interfejs webowy

Po uruchomieniu systemu, interfejs będzie dostępny pod adresem:
**http://localhost:7862**

### 📊 **Zakładki interfejsu:**

1. **📊 Statystyki Storage** - Przegląd bucketów i plików
2. **📋 Kolejka Transkrypcji** - Pliki oczekujące na transkrypcję
3. **🔄 Przetwarzanie Plików** - Automatyczne przetwarzanie załączników
4. **🛠️ Zarządzanie** - Operacje administracyjne

## 📁 Struktura plików

```
python_mixer/
├── storage/
│   ├── enhanced_minio_manager.py      # Główny manager MinIO
│   ├── file_processing_pipeline.py    # Pipeline przetwarzania
│   └── gradio_storage_interface.py    # Interfejs webowy
├── launch_enhanced_storage_system.py  # Główny launcher
├── config.yaml                        # Konfiguracja systemu
└── requirements_storage.txt           # Zależności
```

## 🔧 Konfiguracja

### **MinIO Settings**
```yaml
minio:
  endpoint: "**************:9000"
  access_key_id: "koldbringer"
  secret_access_key: "Blaeritipol1"
  
  # Buckety
  buckets:
    audio: "hvac-audio-files"
    documents: "hvac-documents"
    transcriptions: "hvac-transcriptions"
    processed: "hvac-processed-files"
    temp: "hvac-temp-files"
  
  # Ustawienia wydajności
  part_size: 67108864        # 64MB
  max_retries: 3
  max_file_size: 104857600   # 100MB
```

## 📊 Monitoring i statystyki

### **Dostępne metryki:**
- 📈 Liczba plików w każdym buckecie
- 💾 Rozmiar storage'u (MB/GB)
- 📋 Kolejka transkrypcji
- ⚡ Wskaźnik sukcesu przetwarzania
- 🕐 Czas przetwarzania plików

### **Przykładowe statystyki:**
```json
{
  "buckets": {
    "audio": {
      "name": "hvac-audio-files",
      "files": 371,
      "size": 318767104
    }
  },
  "total_files": 3637,
  "total_size": 318767104
}
```

## 🔄 Workflow przetwarzania

### **1. Pobieranie załączników email**
```python
# Automatyczne <NAME_EMAIL>
attachments = await pipeline.process_dolores_attachments(attachments_dir)
```

### **2. Dekodowanie i upload**
```python
# Dekodowanie base64 i upload do MinIO
uploaded_files = await minio_manager.decode_and_upload_base64_attachments(dir)
```

### **3. Sprawdzanie jakości**
```python
# Walidacja plików audio
quality_check = await pipeline._check_file_quality(metadata)
```

### **4. Przygotowanie do transkrypcji**
```python
# Kolejka transkrypcji
queue = await pipeline.get_transcription_queue()
```

## 🛠️ API i integracja

### **Główne klasy:**

#### **EnhancedMinIOManager**
```python
from storage.enhanced_minio_manager import create_minio_manager

manager = create_minio_manager()
metadata = await manager.upload_m4a_file(file_path)
stats = await manager.get_storage_stats()
```

#### **FileProcessingPipeline**
```python
from storage.file_processing_pipeline import create_processing_pipeline

pipeline = create_processing_pipeline()
report = await pipeline.process_dolores_attachments(attachments_dir)
queue = await pipeline.get_transcription_queue()
```

## 🔒 Bezpieczeństwo

- ✅ **Szyfrowanie połączeń** - Konfiguracja SSL/TLS
- ✅ **Kontrola dostępu** - Klucze dostępu MinIO
- ✅ **Walidacja plików** - Sprawdzanie formatów i rozmiarów
- ✅ **Deduplikacja** - Hash MD5 zapobiega duplikatom
- ✅ **Lifecycle policies** - Automatyczne usuwanie starych plików

## 📈 Wydajność

### **Optymalizacje:**
- 🚀 **Asynchroniczne operacje** - Równoległe przetwarzanie
- 💾 **Chunked upload** - Pliki dzielone na części (64MB)
- 🗜️ **Kompresja** - Automatyczna kompresja metadanych
- ♻️ **Cleanup** - Automatyczne czyszczenie plików tymczasowych

### **Limity:**
- 📏 **Maksymalny rozmiar pliku**: 100MB
- ⏱️ **Timeout połączenia**: 30s
- 🔄 **Maksymalne próby**: 3
- 📅 **Retencja temp files**: 7 dni

## 🐛 Troubleshooting

### **Częste problemy:**

#### **MinIO connection failed**
```bash
# Sprawdź połączenie
python launch_enhanced_storage_system.py --health-check
```

#### **Bucket does not exist**
```python
# Automatyczne tworzenie bucketów
manager._ensure_buckets()
```

#### **File too large**
```yaml
# Zwiększ limit w config.yaml
minio:
  max_file_size: 209715200  # 200MB
```

## 🔮 Przyszłe rozszerzenia

- 🤖 **AI-powered file analysis** - Automatyczna klasyfikacja
- 🔄 **Real-time sync** - Synchronizacja z GoBackend-Kratos
- 📊 **Advanced analytics** - Szczegółowe raporty
- 🌐 **Multi-tenant support** - Obsługa wielu klientów
- 🔐 **Enhanced security** - Szyfrowanie end-to-end

## 📞 Wsparcie

W przypadku problemów:
1. Sprawdź logi w konsoli
2. Uruchom health check
3. Sprawdź konfigurację MinIO
4. Skontaktuj się z zespołem deweloperskim

---

**🚀 Enhanced Storage System - Profesjonalne zarządzanie plikami dla HVAC CRM!**
